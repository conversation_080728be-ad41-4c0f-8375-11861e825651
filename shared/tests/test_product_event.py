#!/usr/bin/env python3
"""
Test script to publish a PRODUCT_CREATED event and verify the inventory service picks it up.
"""

import asyncio
import os
import time
import sys

# Set environment variables for testing
os.environ["REDIS_URL"] = "redis://localhost:6379"
# Use Docker PostgreSQL database - ensure database is running via docker-compose
os.environ["DATABASE_URL"] = "postgresql://postgres:postgres@localhost:5432/fehadan_db"

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.events import Event, PRODUCT_CREATED
from shared.redis_client import publish_event


async def test_product_created_event():
    """Test publishing a PRODUCT_CREATED event."""
    print("🧪 Testing PRODUCT_CREATED event flow...")

    # Sample product data that would come from products_service
    event_data = {
        "product_id": 123,
        "product_name": "Test Chicken Breast",
        "stock_quantity": 50,
        "price": 19.99,
        "currency": "USD",
        "user_id": 1,
        "location_id": 1,
        "reorder_point": 10,
        "created_by": "<EMAIL>",
    }

    # Create the event
    event = Event(type=PRODUCT_CREATED, data=event_data, timestamp=time.time())

    print(f"📤 Publishing event: {event.type}")
    print(f"📋 Event data: {event_data}")

    # Publish to the product_events channel
    await publish_event(event, channel="product_events")

    print("✅ Event published successfully!")
    print("👂 Check the inventory service logs to see if it received the event...")


if __name__ == "__main__":
    asyncio.run(test_product_created_event())
