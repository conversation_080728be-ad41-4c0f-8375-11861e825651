#!/usr/bin/env python3
"""
End-to-end integration test for the event-driven product-inventory flow.
This script:
1. Creates a real product using the products service
2. Verifies that a PRODUCT_CREATED event is published
3. Verifies that the inventory service creates an inventory item
"""

import asyncio
import os
import sys
from decimal import Decimal

# Set environment variables for testing
os.environ["REDIS_URL"] = "redis://localhost:6379"
# Use Docker PostgreSQL database - ensure database is running via docker-compose
os.environ["DATABASE_URL"] = "postgresql://postgres:postgres@localhost:5432/fehadan_db"

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.database import get_db_session, create_tables
from products_service.schemas import ProductCreate
from products_service.services.product import ProductService


class MockUser:
    """Mock user for testing."""

    def __init__(self):
        self.id = 1
        self.email = "<EMAIL>"
        self.username = "testuser"


async def test_complete_flow():
    """Test the complete product creation to inventory flow."""
    print("🧪 Starting end-to-end integration test...")

    # 1. Set up database
    print("📊 Setting up test database...")
    await create_tables()

    # 2. Create a test product
    print("🥩 Creating test product...")

    product_data = ProductCreate(
        name="Premium Chicken Breast",
        description="Fresh, boneless chicken breast perfect for grilling",
        price=Decimal("19.99"),
        currency="USD",
        in_stock=True,
        stock_quantity=25,
        unity_of_measure="lb",
        categories=["meat", "poultry"],
        tags=["fresh", "premium"],
        images=["https://example.com/chicken.jpg"],
    )

    # 3. Use the product service to create the product
    async with get_db_session() as db:
        product_service = ProductService(db)
        mock_user = MockUser()

        # This should trigger the PRODUCT_CREATED event
        created_product = await product_service.create_product(mock_user, product_data)

        if created_product:
            print(f"✅ Product created successfully!")
            print(f"   - ID: {created_product.id}")
            print(f"   - Name: {created_product.name}")
            print(f"   - Stock: {created_product.stock_quantity}")
            print(f"📤 PRODUCT_CREATED event should have been published to Redis")
            print(
                f"👂 Inventory service should create inventory item for product {created_product.id}"
            )

            # Give some time for the event to be processed
            await asyncio.sleep(2)

            print("\n🎉 Integration test completed!")
            print("Check the inventory service logs to verify inventory item creation.")

        else:
            print("❌ Failed to create product")


if __name__ == "__main__":
    asyncio.run(test_complete_flow())
