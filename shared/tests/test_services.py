#!/usr/bin/env python3
"""
Health Check Script for All Microservices
Tests all microservices health endpoints and displays a summary.
"""

import asyncio
import httpx
import json
from datetime import datetime

# Service configuration
SERVICES = [
    {"name": "Auth Service", "port": 8000, "module": "auth_service.main"},
    {"name": "Inventory Service", "port": 8001, "module": "inventory_service.main"},
    {"name": "Products Service", "port": 8002, "module": "products_service.main"},
    {"name": "Orders Service", "port": 8003, "module": "orders_service.main"},
    {"name": "Cart Service", "port": 8004, "module": "cart_service.main"},
    {"name": "Payments Service", "port": 8005, "module": "payments_service.main"},
    {
        "name": "Notifications Service",
        "port": 8006,
        "module": "notifications_service.main",
    },
    {"name": "Reporting Service", "port": 8007, "module": "reporting_service.main"},
]


async def test_service_health(name: str, port: int):
    """Test health check endpoint for a service."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"http://localhost:{port}/", timeout=5.0)
            if response.status_code == 200:
                data = response.json()
                return {
                    "name": name,
                    "port": port,
                    "status": "✅ " + data.get("status", "unknown"),
                    "service": data.get("service", "unknown"),
                    "database": data.get("database", "unknown"),
                    "timestamp": data.get("timestamp", "unknown"),
                }
            else:
                return {
                    "name": name,
                    "port": port,
                    "status": f"❌ HTTP {response.status_code}",
                    "service": "unknown",
                    "database": "unknown",
                    "timestamp": "unknown",
                }
    except Exception as e:
        return {
            "name": name,
            "port": port,
            "status": f"❌ {str(e)[:50]}...",
            "service": "unknown",
            "database": "unknown",
            "timestamp": "unknown",
        }


async def test_all_services():
    """Test all microservices and display results."""
    print("🔍 Testing Fehadan Microservices Health...")
    print("=" * 80)

    # Test all services concurrently
    tasks = [test_service_health(svc["name"], svc["port"]) for svc in SERVICES]
    results = await asyncio.gather(*tasks)

    # Display results
    print(
        f"{'Service':<20} {'Port':<6} {'Status':<15} {'Database':<12} {'Timestamp':<25}"
    )
    print("-" * 80)

    healthy_count = 0
    for result in results:
        status_emoji = "✅" if "✅" in result["status"] else "❌"
        db_status = "🟢" if result["database"] == "connected" else "🔴"

        print(
            f"{result['name']:<20} {result['port']:<6} {result['status']:<15} "
            f"{db_status} {result['database']:<10} {result['timestamp'][:19]}"
        )

        if "✅" in result["status"]:
            healthy_count += 1

    print("-" * 80)
    print(f"Summary: {healthy_count}/{len(SERVICES)} services are healthy")
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(test_all_services())
