#!/usr/bin/env python3
"""
Simple test script to test the products service API
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8012"


def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Health Check: {response.status_code}")
        print(f"Response: {response.json()}")
        return True
    except Exception as e:
        print(f"Health check failed: {e}")
        return False


def test_list_products():
    """Test listing products (should be empty initially)"""
    try:
        response = requests.get(f"{BASE_URL}/products/")
        print(f"List Products: {response.status_code}")
        print(f"Response: {response.json()}")
        return True
    except Exception as e:
        print(f"List products failed: {e}")
        return False


def test_create_product():
    """Test creating a new product"""
    product_data = {
        "name": "Premium Beef Steak",
        "description": "High-quality grass-fed beef steak",
        "price": 25.99,
        "currency": "USD",
        "in_stock": True,
        "stock_quantity": 50,
        "unity_of_measure": "piece",
        "categories": ["meat", "beef", "premium"],
        "tags": ["grass-fed", "organic", "premium"],
        "images": ["beef_steak_1.jpg", "beef_steak_2.jpg"],
        "meat_details": {
            "animal_type": "BEEF",
            "cut_type": "ribeye",
            "grade": "A+",
            "processing_type": "FRESH",
            "preparation": "BONE_IN",
            "packaging_type": "VACUUM",
            "origin": "Texas Ranch",
            "feed_type": "grass-fed",
            "certifications": ["organic", "grass-fed"],
            "lean_ratio": "80/20",
            "shelf_life_fresh": "5 days",
            "shelf_life_frozen": "12 months",
            "storage_instructions": "Keep refrigerated at 32-38°F",
            "cooking_instructions": "Grill for 4-6 minutes each side for medium-rare",
        },
        "nutritional_info": {
            "serving_size": "8 oz",
            "calories": 350,
            "protein": "28g",
            "fat": "25g",
            "saturated_fat": "10g",
            "cholesterol": "80mg",
            "sodium": "65mg",
            "carbohydrates": "0g",
            "fiber": "0g",
            "sugar": "0g",
        },
        "packaging": {
            "weight": "8 oz",
            "package_size": "individual",
            "batch_number": "BATCH001",
        },
    }

    try:
        response = requests.post(f"{BASE_URL}/products/", json=product_data)
        print(f"Create Product: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        if response.status_code == 201:
            return response.json()
        return None
    except Exception as e:
        print(f"Create product failed: {e}")
        return None


def test_get_product(product_id):
    """Test getting a specific product"""
    try:
        response = requests.get(f"{BASE_URL}/products/{product_id}")
        print(f"Get Product {product_id}: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        print(f"Get product failed: {e}")
        return False


def main():
    print("Testing Products Service API")
    print("=" * 40)

    # Test health check
    print("\n1. Testing Health Check:")
    if not test_health_check():
        return

    # Test list products (empty)
    print("\n2. Testing List Products (should be empty):")
    test_list_products()

    # Test create product
    print("\n3. Testing Create Product:")
    created_product = test_create_product()

    if created_product:
        product_id = created_product.get("id")
        print(f"\nCreated product with ID: {product_id}")

        # Test get specific product
        print(f"\n4. Testing Get Product {product_id}:")
        test_get_product(product_id)

        # Test list products again (should have one product)
        print("\n5. Testing List Products (should have one product):")
        test_list_products()

    print("\n" + "=" * 40)
    print("Testing completed!")


if __name__ == "__main__":
    main()
