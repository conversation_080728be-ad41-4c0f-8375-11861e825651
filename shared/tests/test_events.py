#!/usr/bin/env python3
"""
Test script to verify event-driven architecture between products and inventory services.

This script tests:
1. Publishing a PRODUCT_CREATED event
2. Inventory service listening and creating inventory item
3. Verifying the flow works end-to-end
"""

import asyncio
import sys
import os
import time

# Add current directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.events import Event, PRODUCT_CREATED
from shared.redis_client import publish_event, get_redis_client
from products_service.publisher import publish_product_created_event


async def test_product_creation_event():
    """Test publishing a product creation event."""
    print("🧪 Testing Product Creation Event Flow...")

    # Sample product data
    sample_product = {
        "id": 999,  # Test product ID
        "name": "Test Premium Chicken Breast",
        "price": 24.99,
        "currency": "USD",
        "stock_quantity": 50,
        "description": "High-quality chicken breast for testing",
    }

    print(f"📦 Publishing PRODUCT_CREATED event for product: {sample_product['name']}")

    # Publish the event
    success = await publish_product_created_event(
        product_data=sample_product, user_id=1, location_id=1
    )

    if success:
        print("✅ Event published successfully!")
        print("🔍 Check the inventory service logs to see if it processed the event")
        print("📝 Expected behavior:")
        print("   - Inventory service should receive the event")
        print("   - Create an inventory item for product ID 999")
        print("   - Log success message")
    else:
        print("❌ Failed to publish event!")

    return success


async def test_redis_connection():
    """Test Redis connection."""
    print("🔗 Testing Redis connection...")

    try:
        client = await get_redis_client()
        await client.ping()
        print("✅ Redis connection successful!")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {str(e)}")
        print("💡 Make sure Redis is running:")
        print("   - docker run -d -p 6379:6379 redis:alpine")
        print("   - Or update REDIS_URL in your environment")
        return False


async def main():
    """Main test function."""
    print("🚀 Starting Event-Driven Architecture Test\n")

    # Test Redis connection first
    redis_ok = await test_redis_connection()
    if not redis_ok:
        return

    print()

    # Test product creation event
    await test_product_creation_event()

    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\n📋 To verify the full flow:")
    print("1. Start the inventory event listener:")
    print("   cd inventory_service && python event_handlers.py")
    print("2. Run this test script again")
    print("3. Check both services' logs for event processing")
    print("\n🔧 To start Redis if not running:")
    print("   docker run -d -p 6379:6379 redis:alpine")


if __name__ == "__main__":
    asyncio.run(main())
