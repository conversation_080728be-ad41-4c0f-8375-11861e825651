from pydantic_settings import BaseSettings
from pydantic import ConfigDict, computed_field


class Settings(BaseSettings):
    model_config = ConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="allow"
    )

    # Database Configuration
    DATABASE_URL: str

    @computed_field
    @property
    def async_database_url(self) -> str:
        """Convert DATABASE_URL to use async driver (asyncpg for PostgreSQL, aiosqlite for SQLite)"""
        url = self.DATABASE_URL

        # Convert postgresql:// to postgresql+asyncpg:// for async compatibility
        if url.startswith("postgresql://"):
            url = url.replace("postgresql://", "postgresql+asyncpg://", 1)
        elif url.startswith("postgres://"):
            url = url.replace("postgres://", "postgresql+asyncpg://", 1)
        # Convert sqlite:// to sqlite+aiosqlite:// for async compatibility
        elif url.startswith("sqlite://"):
            url = url.replace("sqlite://", "sqlite+aiosqlite://", 1)

        return url

    # JWT Security Configuration
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Password Reset Configuration
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS: int = 1

    # Application Configuration
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Fehadan Meat Processing Backend"

    # CORS Configuration
    ALLOWED_ORIGINS: str = "http://localhost:3000,http://localhost:3001"

    # Frontend URL (for email verification links)
    FRONTEND_URL: str = "http://localhost:3000"

    # Email Configuration (Postmark)
    POSTMARK_SERVER_TOKEN: str = "dev_token_not_used_locally"
    FROM_EMAIL: str = "<EMAIL>"
    EMAIL_ENABLED: bool = True

    # Docker Configuration (for docker-compose)
    POSTGRES_PASSWORD: str = "postgres"


settings = Settings()
