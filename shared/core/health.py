"""
Shared health check utilities for Fehadan Meat Processing Backend

This module provides standardized health check functionality for all microservices.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncEngine
import redis.asyncio as redis
import os


async def check_database_health(engine: AsyncEngine) -> Dict[str, Any]:
    """
    Check database connectivity and health.

    Args:
        engine: SQLAlchemy async engine

    Returns:
        Dict containing database status information
    """
    try:
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        return {"status": "connected", "error": None}
    except Exception as e:
        return {"status": "disconnected", "error": str(e)}


async def check_redis_health(redis_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Check Redis connectivity and health.

    Args:
        redis_url: Redis connection URL

    Returns:
        Dict containing Redis status information
    """
    if not redis_url:
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")

    try:
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        await redis_client.close()
        return {"status": "connected", "error": None}
    except Exception as e:
        return {"status": "disconnected", "error": str(e)}


def create_health_response(
    service_name: str,
    version: str = "1.0.0",
    database_status: Optional[Dict[str, Any]] = None,
    redis_status: Optional[Dict[str, Any]] = None,
    additional_checks: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create a standardized health check response.

    Args:
        service_name: Name of the service
        version: Service version
        database_status: Database health status (if applicable)
        redis_status: Redis health status (if applicable)
        additional_checks: Any additional health checks

    Returns:
        Standardized health response dictionary
    """
    timestamp = datetime.now(timezone.utc).isoformat()

    # Determine overall status
    overall_status = "healthy"

    if database_status and database_status["status"] != "connected":
        overall_status = "unhealthy"

    if redis_status and redis_status["status"] != "connected":
        overall_status = "unhealthy"

    if additional_checks:
        for check_name, check_result in additional_checks.items():
            if (
                isinstance(check_result, dict)
                and check_result.get("status") != "healthy"
            ):
                overall_status = "unhealthy"
                break

    response = {
        "status": overall_status,
        "service": service_name,
        "version": version,
        "timestamp": timestamp,
    }

    if database_status:
        response["database"] = database_status["status"]
        if database_status["error"]:
            response["database_error"] = database_status["error"]

    if redis_status:
        response["redis"] = redis_status["status"]
        if redis_status["error"]:
            response["redis_error"] = redis_status["error"]

    if additional_checks:
        response.update(additional_checks)

    return response


async def comprehensive_health_check(
    service_name: str,
    engine: Optional[AsyncEngine] = None,
    redis_url: Optional[str] = None,
    version: str = "1.0.0",
    additional_checks: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Perform a comprehensive health check for a service.

    Args:
        service_name: Name of the service
        engine: SQLAlchemy async engine (if service uses database)
        redis_url: Redis connection URL (if service uses Redis)
        version: Service version
        additional_checks: Any additional health checks to perform

    Returns:
        Comprehensive health response
    """
    database_status = None
    redis_status = None

    if engine:
        database_status = await check_database_health(engine)

    if redis_url or os.getenv("REDIS_URL"):
        redis_status = await check_redis_health(redis_url)

    return create_health_response(
        service_name=service_name,
        version=version,
        database_status=database_status,
        redis_status=redis_status,
        additional_checks=additional_checks,
    )
