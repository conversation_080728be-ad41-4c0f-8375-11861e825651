# class to generate tokens for various purposes


class TokenGenerator:
    def __init__(self, length=32):
        self.length = length
        self.characters = (
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        )

    def generate_verification_token(self):
        import random

        return "".join(random.choice(self.characters) for _ in range(self.length))

    def generate_password_reset_token(self):
        import random

        return "".join(random.choice(self.characters) for _ in range(self.length))
