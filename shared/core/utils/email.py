"""
Email client for sending emails via Postmark API.

This module provides async email functionality using Postmark's REST API.
All configuration is loaded from environment variables for security.
"""

import logging
from typing import Dict, List, Optional, Any
import aiohttp
from shared.config import settings

logger = logging.getLogger(__name__)


class EmailError(Exception):
    """Custom exception for email-related errors."""

    pass


class EmailClient:
    """
    Async email client using Postmark API.

    Features:
    - Async HTTP requests
    - Proper error handling and logging
    - Environment-based configuration
    - Support for HTML and text emails
    - Attachment support (future)
    """

    def __init__(self):
        self.api_url = "https://api.postmarkapp.com/email"
        self.batch_api_url = "https://api.postmarkapp.com/email/batch"
        self.server_token = settings.POSTMARK_SERVER_TOKEN
        self.from_email = settings.FROM_EMAIL
        self.enabled = settings.EMAIL_ENABLED

        if not self.server_token and self.enabled:
            logger.warning(
                "Email is enabled but POSTMARK_SERVER_TOKEN is not configured"
            )

        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "X-Postmark-Server-Token": self.server_token,
        }

    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_body: Optional[str] = None,
        text_body: Optional[str] = None,
        from_email: Optional[str] = None,
        message_stream: str = "outbound",
        tag: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Send a single email via Postmark API.

        Args:
            to_email: Recipient email address
            subject: Email subject line
            html_body: HTML version of email content
            text_body: Plain text version of email content
            from_email: Sender email (defaults to configured FROM_EMAIL)
            message_stream: Postmark message stream (default: "outbound")
            tag: Optional tag for tracking
            metadata: Optional metadata dictionary

        Returns:
            Dict containing Postmark API response

        Raises:
            EmailError: If email sending fails
        """
        if not self.enabled:
            logger.info(f"Email sending disabled. Would send to {to_email}: {subject}")
            return {"status": "disabled", "message": "Email sending is disabled"}

        if not self.server_token:
            raise EmailError("POSTMARK_SERVER_TOKEN is not configured")

        if not html_body and not text_body:
            raise EmailError("Either html_body or text_body must be provided")

        payload = {
            "From": from_email or self.from_email,
            "To": to_email,
            "Subject": subject,
            "MessageStream": message_stream,
        }

        if html_body:
            payload["HtmlBody"] = html_body
        if text_body:
            payload["TextBody"] = text_body
        if tag:
            payload["Tag"] = tag
        if metadata:
            payload["Metadata"] = metadata

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.api_url,
                    json=payload,
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=30),
                ) as response:
                    response_data = await response.json()

                    if response.status == 200:
                        logger.info(f"Email sent successfully to {to_email}")
                        return response_data
                    else:
                        error_msg = response_data.get("Message", "Unknown error")
                        logger.error(f"Failed to send email to {to_email}: {error_msg}")
                        raise EmailError(f"Postmark API error: {error_msg}")

        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error sending email to {to_email}: {e}")
            raise EmailError(f"Network error sending email: {e}")
        except Exception as e:
            logger.error(f"Unexpected error sending email to {to_email}: {e}")
            raise EmailError(f"Unexpected error sending email: {e}")

    async def send_batch_emails(
        self, emails: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Send multiple emails in a single API call (up to 500 emails).

        Args:
            emails: List of email dictionaries with keys like:
                   {"To": "email", "Subject": "subject", "HtmlBody": "body"}

        Returns:
            List of response dictionaries from Postmark

        Raises:
            EmailError: If batch sending fails
        """
        if not self.enabled:
            logger.info(f"Email sending disabled. Would send {len(emails)} emails")
            return [{"status": "disabled"} for _ in emails]

        if not self.server_token:
            raise EmailError("POSTMARK_SERVER_TOKEN is not configured")

        if len(emails) > 500:
            raise EmailError("Cannot send more than 500 emails in a single batch")

        # Add default From address to all emails if not specified
        for email in emails:
            if "From" not in email:
                email["From"] = self.from_email
            if "MessageStream" not in email:
                email["MessageStream"] = "outbound"

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.batch_api_url,
                    json=emails,
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=60),
                ) as response:
                    response_data = await response.json()

                    if response.status == 200:
                        logger.info(f"Batch of {len(emails)} emails sent successfully")
                        return response_data
                    else:
                        error_msg = response_data.get("Message", "Unknown error")
                        logger.error(f"Failed to send batch emails: {error_msg}")
                        raise EmailError(f"Postmark batch API error: {error_msg}")

        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error sending batch emails: {e}")
            raise EmailError(f"Network error sending batch emails: {e}")
        except Exception as e:
            logger.error(f"Unexpected error sending batch emails: {e}")
            raise EmailError(f"Unexpected error sending batch emails: {e}")

    async def format_email_body(self, path: str, values) -> str:
        """
        args:
            path: The file path to the email template
            values: A dictionary of values to replace in the template

        Business:
            - Read the template file.
            - If path does not exist, raise FileNotFoundError
            - Replace placeholders in the template with values from the dictionary.
            - Return the formatted email body as a string.
        Returns:
            The formatted email body as a string.


        Usage:
            template = await email_client.format_email_body(
                "templates/welcome.html",
                {"username": "John", "link": "https://example.com/verify"}
            )
        """
        try:
            with open(path, "r") as template_file:
                template = template_file.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"Email template not found: {path}")

        # Replace placeholders with values
        for key, value in values.items():
            template = template.replace(f"{{{{ {key} }}}}", str(value))

        return template
