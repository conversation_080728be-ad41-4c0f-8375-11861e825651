"""
Redis client for event publishing and subscription
"""

import json
import os
import redis.asyncio as redis
from typing import Optional
import logging
from .events import Event

logger = logging.getLogger(__name__)


class RedisClient:
    def __init__(self):
        self.redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        self._client: Optional[redis.Redis] = None

    async def get_client(self) -> redis.Redis:
        """Get Redis client instance"""
        if self._client is None:
            self._client = redis.from_url(self.redis_url, decode_responses=True)
        return self._client

    async def close(self):
        """Close Redis connection"""
        if self._client:
            await self._client.close()


# Global Redis client instance
redis_client = RedisClient()


async def get_redis_client() -> redis.Redis:
    """Get Redis client for dependency injection"""
    return await redis_client.get_client()


async def publish_event(event: Event, channel: str = "events"):
    """Publish event to Redis channel"""
    try:
        client = await get_redis_client()
        event_data = json.dumps(event.to_dict())
        await client.publish(channel, event_data)
        logger.info(f"Published event {event.type} to channel {channel}")
    except Exception as e:
        logger.error(f"Failed to publish event {event.type}: {str(e)}")


async def subscribe_to_events(channel: str = "events"):
    """Subscribe to events from Redis channel"""
    try:
        client = await get_redis_client()
        pubsub = client.pubsub()
        await pubsub.subscribe(channel)
        logger.info(f"Subscribed to channel {channel}")
        return pubsub
    except Exception as e:
        logger.error(f"Failed to subscribe to channel {channel}: {str(e)}")
        return None
