from dataclasses import dataclass
from datetime import datetime
from typing import Any

# Event types
USER_REGISTERED = "UserRegistered"
USER_LOGIN = "UserLogin"
USER_LOGOUT = "UserLogout"
PRODUCT_CREATED = "ProductCreated"
PRODUCT_UPDATED = "ProductUpdated"
PRODUCT_DELETED = "ProductDeleted"
INVENTORY_UPDATED = "InventoryUpdated"
INVENTORY_CREATED = "InventoryCreated"
LOW_STOCK_ALERT = "LowStockAlert"
OUT_OF_STOCK = "OutOfStock"
ORDER_CREATED = "OrderCreated"
ORDER_COMPLETED = "OrderCompleted"
ORDER_CANCELLED = "OrderCancelled"
PAYMENT_PROCESSED = "PaymentProcessed"
CART_ITEM_ADDED = "CartItemAdded"
CART_ITEM_REMOVED = "CartItemRemoved"


@dataclass
class Event:
    type: str
    data: dict
    timestamp: float

    def to_dict(self):
        return {"type": self.type, "data": self.data, "timestamp": self.timestamp}
