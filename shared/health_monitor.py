#!/usr/bin/env python3
"""
Health Monitor Service for Fehadan Meat Processing Backend

This service monitors the health of all microservices and provides:
- Centralized health checking for all services
- Health dashboard endpoint
- Alert capabilities when services are down
- Service discovery and monitoring
"""

import asyncio
import logging
import os
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional

import aiohttp
import psycopg2
import redis
from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("health_monitor")

app = FastAPI(title="Fehadan Health Monitor", version="1.0.0")


class ServiceStatus(BaseModel):
    name: str
    url: str
    status: str  # "healthy", "unhealthy", "unknown"
    response_time: Optional[float] = None
    last_checked: datetime
    error_message: Optional[str] = None


class SystemStatus(BaseModel):
    overall_status: str
    timestamp: datetime
    services: List[ServiceStatus]
    database_status: str
    redis_status: str
    uptime_seconds: int


class HealthMonitor:
    def __init__(self):
        self.services_to_monitor = self._parse_services()
        self.start_time = time.time()
        self.database_url = os.getenv("DATABASE_URL")
        self.redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        self.check_interval = int(os.getenv("CHECK_INTERVAL", "30"))
        self.service_statuses: Dict[str, ServiceStatus] = {}

    def _parse_services(self) -> Dict[str, str]:
        """Parse services from environment variable."""
        services_env = os.getenv("SERVICES_TO_MONITOR", "")
        services = {}

        for service_config in services_env.split(","):
            if ":" in service_config:
                service_name, port = service_config.strip().split(":")
                services[service_name] = f"http://{service_name}:{port}"

        return services

    async def check_service_health(self, name: str, url: str) -> ServiceStatus:
        """Check health of a single service."""
        start_time = time.time()

        try:
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10)
            ) as session:
                async with session.get(f"{url}/") as response:
                    response_time = time.time() - start_time

                    if response.status == 200:
                        data = await response.json()
                        if data.get("status") == "healthy":
                            return ServiceStatus(
                                name=name,
                                url=url,
                                status="healthy",
                                response_time=response_time,
                                last_checked=datetime.now(timezone.utc),
                            )

                    return ServiceStatus(
                        name=name,
                        url=url,
                        status="unhealthy",
                        response_time=response_time,
                        last_checked=datetime.now(timezone.utc),
                        error_message=f"HTTP {response.status}",
                    )

        except Exception as e:
            response_time = time.time() - start_time
            return ServiceStatus(
                name=name,
                url=url,
                status="unhealthy",
                response_time=response_time,
                last_checked=datetime.now(timezone.utc),
                error_message=str(e),
            )

    def check_database_health(self) -> str:
        """Check PostgreSQL database health."""
        try:
            conn = psycopg2.connect(self.database_url)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            conn.close()
            return "healthy"
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return "unhealthy"

    def check_redis_health(self) -> str:
        """Check Redis health."""
        try:
            r = redis.from_url(self.redis_url)
            r.ping()
            return "healthy"
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return "unhealthy"

    async def monitor_services(self):
        """Continuously monitor all services."""
        while True:
            try:
                logger.info("Running health checks...")

                # Check all services
                tasks = [
                    self.check_service_health(name, url)
                    for name, url in self.services_to_monitor.items()
                ]

                if tasks:
                    service_results = await asyncio.gather(
                        *tasks, return_exceptions=True
                    )

                    for result in service_results:
                        if isinstance(result, ServiceStatus):
                            self.service_statuses[result.name] = result
                            logger.info(
                                f"{result.name}: {result.status} ({result.response_time:.3f}s)"
                            )

                # Check database and Redis
                db_status = self.check_database_health()
                redis_status = self.check_redis_health()

                logger.info(f"Database: {db_status}, Redis: {redis_status}")

                # Log overall system status
                unhealthy_services = [
                    status.name
                    for status in self.service_statuses.values()
                    if status.status != "healthy"
                ]

                if (
                    unhealthy_services
                    or db_status != "healthy"
                    or redis_status != "healthy"
                ):
                    logger.warning(
                        f"System issues detected: services={unhealthy_services}, db={db_status}, redis={redis_status}"
                    )
                else:
                    logger.info("All systems healthy")

            except Exception as e:
                logger.error(f"Error during health monitoring: {e}")

            await asyncio.sleep(self.check_interval)

    def get_system_status(self) -> SystemStatus:
        """Get current system status."""
        # Determine overall status
        db_status = self.check_database_health()
        redis_status = self.check_redis_health()

        service_statuses = list(self.service_statuses.values())
        unhealthy_services = [s for s in service_statuses if s.status != "healthy"]

        if unhealthy_services or db_status != "healthy" or redis_status != "healthy":
            overall_status = "unhealthy"
        elif not service_statuses:
            overall_status = "unknown"
        else:
            overall_status = "healthy"

        return SystemStatus(
            overall_status=overall_status,
            timestamp=datetime.now(timezone.utc),
            services=service_statuses,
            database_status=db_status,
            redis_status=redis_status,
            uptime_seconds=int(time.time() - self.start_time),
        )


# Global health monitor instance
health_monitor = HealthMonitor()


@app.on_event("startup")
async def startup_event():
    """Start the health monitoring task."""
    asyncio.create_task(health_monitor.monitor_services())
    logger.info("Health monitor started")


@app.get("/health")
async def health_check():
    """Health check endpoint for this service."""
    return {
        "status": "healthy",
        "service": "health-monitor",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": int(time.time() - health_monitor.start_time),
    }


@app.get("/status", response_model=SystemStatus)
async def get_status():
    """Get detailed system status."""
    return health_monitor.get_system_status()


@app.get("/", response_class=HTMLResponse)
async def dashboard():
    """Health dashboard HTML page."""
    system_status = health_monitor.get_system_status()

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Fehadan Health Monitor</title>
        <meta http-equiv="refresh" content="30">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ text-align: center; margin-bottom: 30px; }}
            .status-card {{ background: white; padding: 20px; margin: 10px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            .healthy {{ border-left: 5px solid #4CAF50; }}
            .unhealthy {{ border-left: 5px solid #f44336; }}
            .unknown {{ border-left: 5px solid #FF9800; }}
            .status-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }}
            .overall-status {{ font-size: 24px; font-weight: bold; text-align: center; padding: 20px; margin-bottom: 20px; }}
            .service-name {{ font-weight: bold; font-size: 18px; }}
            .service-url {{ color: #666; font-size: 14px; }}
            .service-details {{ margin-top: 10px; }}
            .timestamp {{ color: #888; font-size: 12px; }}
            .error {{ color: #f44336; font-size: 12px; margin-top: 5px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏥 Fehadan Health Monitor</h1>
                <p>Last updated: {system_status.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                <p>Uptime: {system_status.uptime_seconds // 3600}h {(system_status.uptime_seconds % 3600) // 60}m</p>
            </div>
            
            <div class="status-card overall-status {system_status.overall_status}">
                Overall System Status: {system_status.overall_status.upper()}
            </div>
            
            <div class="status-grid">
                <div class="status-card {system_status.database_status}">
                    <div class="service-name">🗄️ PostgreSQL Database</div>
                    <div class="service-details">Status: {system_status.database_status}</div>
                </div>
                
                <div class="status-card {system_status.redis_status}">
                    <div class="service-name">🔴 Redis Cache</div>
                    <div class="service-details">Status: {system_status.redis_status}</div>
                </div>
    """

    for service in system_status.services:
        response_time = (
            f"{service.response_time:.3f}s" if service.response_time else "N/A"
        )
        error_msg = (
            f'<div class="error">Error: {service.error_message}</div>'
            if service.error_message
            else ""
        )

        html_content += f"""
                <div class="status-card {service.status}">
                    <div class="service-name">🚀 {service.name.replace('_', ' ').title()}</div>
                    <div class="service-url">{service.url}</div>
                    <div class="service-details">
                        Status: {service.status}<br>
                        Response Time: {response_time}<br>
                        <span class="timestamp">Last checked: {service.last_checked.strftime('%H:%M:%S UTC')}</span>
                    </div>
                    {error_msg}
                </div>
        """

    html_content += """
            </div>
        </div>
    </body>
    </html>
    """

    return html_content


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8080)
