services:
  # Database migration initialization - runs automatically
  migration_init:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: migration_init
    depends_on:
      postgres_db:
        condition: service_healthy
    environment:
      - DATABASE_URL=${DATABASE_URL}
    env_file:
      - .env
    working_dir: /app
    command: >
      sh -c "
        echo 'Checking for new migrations...' &&
        alembic revision --autogenerate -m 'Auto-generated migration' || echo 'No model changes detected' &&
        echo 'Applying migrations...' &&
        alembic upgrade head &&
        echo 'Migrations complete!'
      "
    restart: "no"
    volumes:
      - ./migrations:/app/migrations

  auth_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: auth_service
    ports:
      - "8000:8000"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=${JWT_ALGORITHM}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
      - EMAIL_ENABLED=${EMAIL_ENABLED}
    env_file:
      - .env
    restart: always
    command: uvicorn auth_service.main:app --host 0.0.0.0 --port 8000

  inventory_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: inventory_service
    ports:
      - "8001:8001"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
    env_file:
      - .env
    restart: always
    command: uvicorn inventory_service.main:app --host 0.0.0.0 --port 8001

  products_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: products_service
    ports:
      - "8002:8002"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
    env_file:
      - .env
    restart: always
    command: uvicorn products_service.main:app --host 0.0.0.0 --port 8002

  orders_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: orders_service
    ports:
      - "8003:8003"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
    env_file:
      - .env
    restart: always
    command: uvicorn orders_service.main:app --host 0.0.0.0 --port 8003

  cart_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: cart_service
    ports:
      - "8004:8004"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
    env_file:
      - .env
    restart: always
    command: uvicorn cart_service.main:app --host 0.0.0.0 --port 8004

  payments_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: payments_service
    ports:
      - "8005:8005"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
    env_file:
      - .env
    restart: always
    command: uvicorn payments_service.main:app --host 0.0.0.0 --port 8005

  notifications_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: notifications_service
    ports:
      - "8006:8006"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
    env_file:
      - .env
    restart: always
    command: uvicorn notifications_service.main:app --host 0.0.0.0 --port 8006

  reporting_service:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: reporting_service
    ports:
      - "8007:8007"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration_init:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - PROJECT_NAME=${PROJECT_NAME}
    env_file:
      - .env
    restart: always
    command: uvicorn reporting_service.main:app --host 0.0.0.0 --port 8007

  postgres_db:
    image: postgres:15
    container_name: postgres_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: fehadan_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    container_name: redis
    ports:
      - "6379:6379"
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  health_monitor:
    build: .
    image: fehdan-meat-processing-backend:latest
    container_name: health_monitor
    depends_on:
      - auth_service
      - inventory_service
      - products_service
      - orders_service
      - cart_service
      - payments_service
      - notifications_service
      - reporting_service
    environment:
      - SERVICES_TO_MONITOR=auth_service:8000,inventory_service:8001,products_service:8002,orders_service:8003,cart_service:8004,payments_service:8005,notifications_service:8006,reporting_service:8007
      - CHECK_INTERVAL=30
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    env_file:
      - .env
    restart: always
    command: python -m shared.health_monitor
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import requests; requests.get('http://localhost:8080/health')",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    ports:
      - "8080:8080"

volumes:
  postgres_data:
