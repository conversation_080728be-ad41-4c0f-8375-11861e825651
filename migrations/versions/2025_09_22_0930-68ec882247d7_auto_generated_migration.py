"""Auto-generated migration

Revision ID: 68ec882247d7
Revises: c4cc5e1f18ac
Create Date: 2025-09-22 09:30:40.630199

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '68ec882247d7'
down_revision: Union[str, None] = 'c4cc5e1f18ac'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###