"""Auto-generated migration

Revision ID: 409a4bdd34ea
Revises: af6f716d84ee
Create Date: 2025-09-22 08:17:53.717855

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '409a4bdd34ea'
down_revision: Union[str, None] = 'af6f716d84ee'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###