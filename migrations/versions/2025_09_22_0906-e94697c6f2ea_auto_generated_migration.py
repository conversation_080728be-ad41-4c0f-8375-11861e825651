"""Auto-generated migration

Revision ID: e94697c6f2ea
Revises: 409a4bdd34ea
Create Date: 2025-09-22 09:06:27.057086

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e94697c6f2ea'
down_revision: Union[str, None] = '409a4bdd34ea'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###