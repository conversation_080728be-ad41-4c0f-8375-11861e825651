"""Auto-generated migration

Revision ID: 8751ee0a0e2b
Revises: e6e36255e135
Create Date: 2025-09-22 10:49:24.352451

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8751ee0a0e2b'
down_revision: Union[str, None] = 'e6e36255e135'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###