"""Auto-generated migration

Revision ID: c7e1d9ec5cf3
Revises: 6d0b72760387
Create Date: 2025-09-22 06:08:00.193768

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c7e1d9ec5cf3'
down_revision: Union[str, None] = '6d0b72760387'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###