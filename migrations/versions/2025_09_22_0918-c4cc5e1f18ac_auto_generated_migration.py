"""Auto-generated migration

Revision ID: c4cc5e1f18ac
Revises: e94697c6f2ea
Create Date: 2025-09-22 09:18:31.749099

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c4cc5e1f18ac'
down_revision: Union[str, None] = 'e94697c6f2ea'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###