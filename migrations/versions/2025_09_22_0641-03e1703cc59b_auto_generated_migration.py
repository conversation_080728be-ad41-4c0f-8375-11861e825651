"""Auto-generated migration

Revision ID: 03e1703cc59b
Revises: c7e1d9ec5cf3
Create Date: 2025-09-22 06:41:49.217522

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '03e1703cc59b'
down_revision: Union[str, None] = 'c7e1d9ec5cf3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###