FROM python:3.11-slim

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y build-essential && rm -rf /var/lib/apt/lists/*

# Copy requirements and install
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Expose port (default FastAPI)
EXPOSE 8000

# Default command - can be overridden in docker-compose
CMD ["uvicorn", "auth_service.main:app", "--host", "0.0.0.0", "--port", "8000"]