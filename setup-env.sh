#!/bin/bash

# Environment setup script for Fehadan Meat Processing Backend
# This script helps switch between different environment configurations

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

usage() {
    echo "Usage: $0 <environment>"
    echo ""
    echo "Environments:"
    echo "  local       - Local development with Docker PostgreSQL"
    echo "  production  - Production configuration"
    echo "  test        - Testing configuration"
    echo ""
    echo "Examples:"
    echo "  $0 local        # Setup for local development"
    echo "  $0 production   # Setup for production deployment"
}

setup_local() {
    echo "🔧 Setting up local development environment..."
    
    if [ ! -f ".env.local" ]; then
        echo "❌ .env.local file not found!"
        echo "Create .env.local file with local development configuration."
        exit 1
    fi
    
    cp .env.local .env
    echo "✅ Copied .env.local to .env"
    
    echo "🐳 Building and starting all services (includes auto-migrations)..."
    docker-compose up -d --build
    
    echo "✅ Local environment ready!"
    echo ""
    echo "📋 What just happened:"
    echo "  ✅ Built Docker images"  
    echo "  ✅ Started PostgreSQL and Redis"
    echo "  ✅ Auto-generated migrations (if needed)"
    echo "  ✅ Applied all migrations"
    echo "  ✅ Started all microservices"
    echo "  ✅ Started health monitor"
    echo ""
    echo "🌐 Access points:"
    echo "  • Health Dashboard: http://localhost:8080"
    echo "  • Auth Service: http://localhost:8000/docs"
    echo "  • All Services: docker-compose ps"
}

setup_production() {
    echo "🚀 Setting up production environment..."
    
    if [ ! -f ".env" ]; then
        echo "❌ .env file not found!"
        echo "Create .env file with production configuration."
        exit 1
    fi
    
    echo "✅ Using existing .env file for production"
    
    echo "🐳 Building and deploying all services..."
    docker-compose up -d --build
    
    echo "✅ Production environment ready!"
    echo ""
    echo "📋 What was deployed:"
    echo "  ✅ Built production Docker images"
    echo "  ✅ Started infrastructure (PostgreSQL, Redis)"
    echo "  ✅ Auto-generated and applied migrations"
    echo "  ✅ Started all microservices"
    echo "  ✅ Started health monitoring"
    echo ""
    echo "🌐 Management:"
    echo "  • Health Dashboard: http://localhost:8080"
    echo "  • Check status: docker-compose ps"
    echo "  • View logs: docker-compose logs [service]"
}

setup_test() {
    echo "🧪 Setting up test environment..."
    
    # Create a test environment file
    cat > .env.test << EOF
# Test Environment Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/fehadan_test_db
REDIS_URL=redis://localhost:6379/1
JWT_SECRET_KEY=test_secret_key_do_not_use_in_production
DEBUG=true
EMAIL_ENABLED=false
POSTGRES_PASSWORD=postgres
EOF
    
    cp .env.test .env
    echo "✅ Created and activated test environment"
    
    echo "🐳 Building and starting test environment..."
    docker-compose up -d --build
    
    # Create test database
    echo "🗄️  Setting up test database..."
    sleep 5  # Wait for postgres to be ready
    docker-compose exec postgres_db psql -U postgres -c "DROP DATABASE IF EXISTS fehadan_test_db;" 2>/dev/null || true
    docker-compose exec postgres_db psql -U postgres -c "CREATE DATABASE fehadan_test_db;"
    
    # Re-run migrations for test database
    docker-compose restart migration_init
    
    echo "✅ Test environment ready!"
    echo ""
    echo "📋 What was set up:"
    echo "  ✅ Test environment configuration"
    echo "  ✅ Test database: fehadan_test_db"
    echo "  ✅ All services with test settings"
    echo "  ✅ Auto-generated and applied migrations"
    echo ""
    echo "🧪 Run tests with: pytest"
    echo "🌐 Health Dashboard: http://localhost:8080"
}

# Main script logic
case "${1:-}" in
    "local")
        setup_local
        ;;
    "production")
        setup_production
        ;;
    "test")
        setup_test
        ;;
    *)
        usage
        exit 1
        ;;
esac