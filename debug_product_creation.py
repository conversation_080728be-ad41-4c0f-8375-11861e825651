#!/usr/bin/env python3
"""
Debug script to test product creation with the exact data from our API call.
"""

import asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from products_service.repositories.product import ProductRepository
from products_service.schemas import ProductCreate


async def test_product_creation():
    """Test product creation with the exact data from our API call."""

    # Use Docker PostgreSQL database - ensure database is running via docker-compose
    database_url = "postgresql+asyncpg://postgres:postgres@localhost:5432/fehadan_db"
    engine = create_async_engine(database_url, echo=True)

    # Create the session
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        product_repo = ProductRepository(session)

        # Create the exact same data as our API call
        product_data = ProductCreate(
            name="Test Beef Product",
            category="meat",
            price=Decimal("25.99"),
            description="A test beef product for event testing",
            animal_type="cow",
            weight=1.5,
            processing_type="fresh",
            preparation_type="whole",
        )

        print(f"Product data: {product_data}")
        print(f"Product data dict: {product_data.model_dump()}")

        try:
            result = await product_repo.create_product(product_data)
            print(f"Success! Created product: {result}")
            return result
        except Exception as e:
            print(f"Error creating product: {e}")
            print(f"Exception type: {type(e)}")
            import traceback

            traceback.print_exc()
            return None


if __name__ == "__main__":
    result = asyncio.run(test_product_creation())
    print(f"Final result: {result}")
