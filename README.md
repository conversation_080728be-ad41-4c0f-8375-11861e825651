# Fehadan Microservices Platform

A comprehensive microservices platform for meat processing business management, built with FastAPI, PostgreSQL, Redis, and Docker.

## � Key Features

- **🐳 Docker-First**: Complete containerized workflow - no local dependencies
- **🗄️ Auto-Migrations**: Database migrations run automatically on startup
- **🏥 Health Monitoring**: Built-in health dashboard and monitoring
- **🔄 Zero-Downtime**: Services start only after migrations complete
- **📊 Real-time Dashboard**: Web-based service monitoring at http://localhost:8080
- **🛡️ Production-Ready**: Comprehensive logging, error handling, and monitoring

## �🏗️ Architecture Overview

The platform consists of 8 microservices, each handling specific business domains:

| Service                   | Port | Responsibility                                             |
| ------------------------- | ---- | ---------------------------------------------------------- |
| **auth_service**          | 8000 | User authentication, authorization, user management        |
| **inventory_service**     | 8001 | Inventory tracking, stock management, warehouse operations |
| **products_service**      | 8002 | Product catalog, pricing, product information management   |
| **orders_service**        | 8003 | Order processing, order lifecycle management               |
| **cart_service**          | 8004 | Shopping cart operations, cart management                  |
| **payments_service**      | 8005 | Payment processing, billing, financial transactions        |
| **notifications_service** | 8006 | Email, SMS, push notifications                             |
| **reporting_service**     | 8007 | Analytics, reporting, business intelligence                |

**Plus Infrastructure:**

- **PostgreSQL Database** (port 5432) - Centralized data storage
- **Redis Cache** (port 6379) - Caching and session management
- **Health Monitor** (port 8080) - Service monitoring dashboard

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Git

**✨ Zero Local Dependencies!** No Python, Node.js, Alembic, or other tools needed locally.

### � Super Simple Workflow

Everything happens automatically when you build and start:

```bash
# Option 1: Use setup script (recommended)
./setup-env.sh local

# Option 2: Direct docker-compose
docker-compose up -d --build
```

**That's it!** When you run `docker-compose up -d --build`, it automatically:

1. 🏗️ Builds all Docker images
2. 🐳 Starts PostgreSQL and Redis
3. 🔄 Auto-generates migrations (if models changed)
4. 📋 Applies all migrations
5. 🚀 Starts all microservices
6. 🏥 Starts health monitoring at http://localhost:8080

# Check status and health

./docker.sh status
./docker.sh health

# View health dashboard

./docker.sh dashboard # Opens http://localhost:8080

````

### Alternative: Direct Docker Compose

```bash
# Quick start (requires .env file)
docker-compose up -d

# Or step by step
docker-compose up -d postgres_db redis  # Infrastructure
### Alternative: Step by Step

```bash
# Step by step approach
docker-compose up -d postgres_db redis  # Infrastructure only
docker-compose up -d                    # Everything (migrations auto-run)
````

### Development

```bash
# Full Docker development (recommended)
docker-compose up -d --build

# Check what's running
docker-compose ps

# View logs
docker-compose logs -f [service_name]

# Access health dashboard
open http://localhost:8080
```

## 🔄 Automatic Database Migrations

### How It Works

**Migrations happen automatically when you start services!**

1. 🏗️ `docker-compose up -d --build` builds images
2. 🗄️ `migration_init` container starts first
3. 🔍 Checks for model changes and auto-generates migrations
4. 📋 Applies all pending migrations
5. ✅ Services start only after migrations complete

### No Manual Migration Commands Needed!

The system automatically:

- Detects changes in your SQLAlchemy models
- Generates migration files in `migrations/versions/`
- Applies migrations before starting services
- Ensures database schema is always current

### Manual Override (if needed)

```bash
# Force regenerate migrations
docker-compose run --rm migration_init alembic revision --autogenerate -m "Manual migration"

# Check migration status
docker-compose run --rm migration_init alembic current

# View migration history
```

# Run migrations manually

docker-compose --profile migration up migration

# View health dashboard

open http://localhost:8080

````

### Development

```bash
# Option 1: Full Docker development
./docker.sh start

# Option 2: Local development (requires Python setup)
./setup-env.sh local
uvicorn auth_service.main:app --reload --port 8000

# Option 3: Mixed approach
docker-compose up -d postgres_db redis  # Infrastructure in Docker
uvicorn auth_service.main:app --reload   # Service locally
````

## 🗄️ Database Migrations (Docker-First)

### 🐳 Docker Migration Commands

```bash
# Create a new migration (Docker-based)
./docker.sh migration-create "Add user table"

# Run all pending migrations
./docker.sh migrate

# Show current migration status
./docker.sh migration-current

# Show migration history
./docker.sh migration-history

# Downgrade one migration
./docker.sh migration-downgrade -1

# Reset database (WARNING: destroys all data)
./docker.sh migration-reset
```

### Alternative: Local Migration Commands

If you have Python/Alembic installed locally:

```bash
./migrate.sh create "Add user table"
./migrate.sh migrate
./migrate.sh current
./migrate.sh history
```

### Migration Workflow

1. **Automatic Migrations**: All services automatically wait for migrations to complete before starting
2. **Docker-First**: Create migrations using `./docker.sh migration-create "description"`
3. **Production**: Migrations run automatically during deployment
4. **Testing**: Test environment includes fresh migrations

### Migration Architecture

- **Centralized**: Single Alembic configuration for all services
- **Automated**: Services depend on migration completion
- **Safe**: Migrations run before any service starts
- **Versioned**: Full migration history and rollback support
- **Docker-Native**: No local Python dependencies required

## 🐳 Docker Commands Reference

### 🚀 Main Operations

```bash
# Environment setup
./docker.sh setup local        # Set up local development
./docker.sh setup production   # Set up production
./docker.sh setup test         # Set up test environment

# Service management
./docker.sh build             # Build all Docker images
./docker.sh start             # Start all services (includes migrations)
./docker.sh stop              # Stop all services
./docker.sh restart           # Restart all services
./docker.sh status            # Show service status
```

### 🗄️ Database & Migration Operations

```bash
# Migration commands
./docker.sh migrate                     # Run pending migrations
./docker.sh migration-create "message" # Create new migration
./docker.sh migration-current          # Show current revision
./docker.sh migration-history          # Show migration history
./docker.sh migration-downgrade -1     # Downgrade one revision
./docker.sh migration-reset            # Reset database (destructive)

# Database access
./docker.sh db-shell           # Open PostgreSQL shell
```

### 🏥 Health & Monitoring

```bash
# Health checks
./docker.sh health             # Check all service health
./docker.sh dashboard          # Open health dashboard (http://localhost:8080)
./docker.sh test-health        # Run comprehensive health tests

# Logging
./docker.sh logs               # Show all service logs
./docker.sh logs auth_service  # Show specific service logs
```

### 🧹 Utility Operations

```bash
# Cleanup & maintenance
./docker.sh clean             # Clean containers and volumes
./docker.sh rebuild           # Clean rebuild all images

# Debugging
./docker.sh shell migration   # Open shell in migration container
./docker.sh shell auth_service # Open shell in service container
```

### 🔧 Development Workflow

```bash
# Daily development
./docker.sh setup local       # One-time setup
./docker.sh start            # Start everything
./docker.sh logs auth_service # Debug specific service

# Database changes
./docker.sh migration-create "Add new table"
./docker.sh migrate          # Apply changes

# Testing changes
./docker.sh restart         # Restart after code changes
./docker.sh health          # Verify everything works
```

## 🔧 Development

### Option 1: Full Docker Development (Recommended)

```bash
# Complete Docker-based development
./docker.sh setup local
./docker.sh start
./docker.sh dashboard  # Monitor health

# Make code changes, then:
./docker.sh restart
```

### Option 2: Hybrid Development

```bash
# Infrastructure in Docker, services locally
docker-compose up -d postgres_db redis
./docker.sh migrate

# Run services locally (requires Python setup)
uvicorn auth_service.main:app --reload --port 8000
```

### Option 3: Traditional Local Development

1. **Create Virtual Environment**

```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

2. **Start Infrastructure & Migrate**

```bash
docker-compose up -d postgres_db redis
./migrate.sh migrate
```

3. **Run Individual Service**

```bash
uvicorn auth_service.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🗄️ Database Configuration

The platform uses PostgreSQL as the primary database, running in Docker containers. All services connect to the same database using the `DATABASE_URL` environment variable.

### Database Setup

**Production/Docker:**

```bash
# All services use the same PostgreSQL database
DATABASE_URL=***********************************************/fehadan_db
```

**Local Development:**

```bash
# For local development with Docker database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/fehadan_db

# Copy the local environment file
cp .env.local .env
```

### Environment Files

- `.env` - Production configuration (used with `docker-compose.yml`)
- `.env.local` - Local development configuration (used with `docker-compose.local.yml`)

### Database Commands

```bash
# Start database only
docker-compose up -d postgres_db

# Connect to database
docker exec -it postgres_db psql -U postgres -d fehadan_db

# View database logs
docker logs postgres_db

# Reset database (WARNING: destroys all data)
docker-compose down -v
docker-compose up -d postgres_db
```

### Testing

Tests can use either in-memory SQLite (default) or PostgreSQL:

```bash
# Run tests with in-memory SQLite (default)
pytest

# Run tests with PostgreSQL (ensure database is running)
export TEST_DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/fehadan_db"
pytest
```

### Migrations

```bash
# Run migrations for all services
./deploy.sh migrate

# Connect to database
docker-compose exec db psql -U postgres -d auth_db
```

### Backup and Restore

```bash
# Backup database
docker-compose exec db pg_dump -U postgres auth_db > backup.sql

# Restore database
docker-compose exec -T db psql -U postgres auth_db < backup.sql
```

## 🏥 Health Monitoring

### Health Dashboard

Access the centralized health monitoring dashboard:

```bash
# Open health dashboard
open http://localhost:8080

# Check system status via API
curl http://localhost:8080/status
```

### Health Check Service

The integrated health monitor provides:

- **Real-time Monitoring**: Continuous health checks for all services
- **Visual Dashboard**: Web interface showing service status
- **Centralized Logging**: Health status logging and alerts
- **Service Discovery**: Automatic detection of service health

### Individual Service Health

Each service provides standardized health endpoints:

```json
{
	"status": "healthy",
	"service": "auth-microservice",
	"version": "1.0.0",
	"database": "connected",
	"redis": "connected",
	"timestamp": "2025-09-19T13:56:29.737223Z"
}
```

### Monitoring Endpoints

- **Health Dashboard**: `http://localhost:8080` - Web interface
- **System Status**: `http://localhost:8080/status` - JSON API
- **Individual Services**: `http://localhost:{port}/` - Service health
- **Service Documentation**: `http://localhost:{port}/docs` - Swagger UI

### Health Check Architecture

- **Automated**: Health checks run every 30 seconds
- **Comprehensive**: Database, Redis, and service connectivity
- **Alerting**: Logs warnings when services are unhealthy
- **Containerized**: Runs as a Docker service

## 🐳 Docker Configuration

### Services Overview

```yaml
services:
      db: # PostgreSQL database
      redis: # Redis cache
      auth_service: # Authentication service
      inventory_service: # Inventory management
      products_service: # Product catalog
      orders_service: # Order processing
      cart_service: # Shopping cart
      payments_service: # Payment processing
      notifications_service: # Notifications
      reporting_service: # Analytics & reporting
```

### Network Configuration

All services communicate through the `fehadan-network` Docker network with proper service discovery.

## 🔒 Security Considerations

- JWT tokens for authentication
- Environment-based configuration
- Database connection security
- CORS configuration for API access
- Docker network isolation

## 📊 Logging and Monitoring

### Log Levels

- **DEBUG**: Detailed debugging information
- **INFO**: General information
- **WARNING**: Warning messages
- **ERROR**: Error messages
- **CRITICAL**: Critical errors

### Accessing Logs

```bash
# All services logs
./deploy.sh logs

# Specific service logs
./deploy.sh logs auth

# Follow logs in real-time
docker-compose logs -f auth_service
```

## 🔧 Troubleshooting

### Common Issues

1. **Service Not Starting**

      ```bash
      ./deploy.sh status
      ./deploy.sh logs [service]
      ```

2. **Database Connection Issues**

      ```bash
      docker-compose exec db pg_isready -U postgres
      ```

3. **Port Conflicts**

      - Check if ports 8000-8007 are available
      - Modify port mappings in `docker-compose.yml`

4. **Memory Issues**
      ```bash
      docker system prune
      ./deploy.sh clean
      ```

### Health Check Failures

```bash
# Test specific service
./deploy.sh health products

# Comprehensive test
./deploy.sh test

# Manual health check
curl http://localhost:8000/
```

## 🚀 Production Deployment

1. **Environment Configuration**

      - Update `.env` with production values
      - Set secure JWT secret keys
      - Configure production database

2. **Security Hardening**

      - Enable HTTPS/TLS
      - Set up firewall rules
      - Configure authentication

3. **Monitoring Setup**

      - Set up log aggregation
      - Configure health monitoring
      - Set up alerting

4. **Backup Strategy**
      - Database backups
      - Code repository backups
      - Configuration backups

## 📚 API Documentation

Each service provides interactive API documentation:

- **Auth Service**: http://localhost:8000/docs
- **Inventory Service**: http://localhost:8001/docs
- **Products Service**: http://localhost:8002/docs
- **Orders Service**: http://localhost:8003/docs
- **Cart Service**: http://localhost:8004/docs
- **Payments Service**: http://localhost:8005/docs
- **Notifications Service**: http://localhost:8006/docs
- **Reporting Service**: http://localhost:8007/docs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test
4. Submit a pull request

## 📞 Support

For support and questions, please refer to the project documentation or contact the development team.

---

**Fehadan Microservices Platform** - Built with ❤️ for efficient meat processing business management.
