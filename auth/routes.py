from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from auth_service.schemas import (
    UserCreate,
    UserLogin,
    UserResponse,
    TokenResponse,
    PasswordReset,
    PasswordResetConfirm,
    EmailVerificationRequest,
)
from auth_service.services.auth_service import AuthService
from auth_service.repositories.user_repository import UserRepository
from shared.database import get_db

router = APIRouter()


# Dependency to get AuthService
async def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    user_repo = UserRepository(db)
    return AuthService(user_repo)


# register
@router.post(
    "/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED
)
async def register(
    user: UserCreate, auth_service: AuthService = Depends(get_auth_service)
):
    new_user = await auth_service.register_user(user)
    if not new_user:
        raise HTTPException(status_code=400, detail="Registration failed")
    return new_user


# login
@router.post("/login", response_model=TokenResponse)
async def login(user: UserLogin, auth_service: AuthService = Depends(get_auth_service)):
    authenticated_user = await auth_service.authenticate_user(user)
    if not authenticated_user:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    return authenticated_user


# refresh_token
@router.post("/refresh-token", response_model=TokenResponse)
async def refresh_token(
    refresh_token: str, auth_service: AuthService = Depends(get_auth_service)
):
    refreshed_tokens = await auth_service.refresh_token(refresh_token)
    if not refreshed_tokens:
        raise HTTPException(status_code=401, detail="Invalid token")
    return refreshed_tokens


# request_password_reset
@router.post("/request-password-reset")
async def request_password_reset(
    request: PasswordReset, auth_service: AuthService = Depends(get_auth_service)
):
    success = await auth_service.request_password_reset(request.email)
    if not success:
        raise HTTPException(status_code=400, detail="Password reset request failed")
    return {"message": "Password reset email sent"}


# confirm_password_reset
@router.post("/confirm-password-reset")
async def confirm_password_reset(
    request: PasswordResetConfirm, auth_service: AuthService = Depends(get_auth_service)
):
    success = await auth_service.confirm_password_reset(
        request.token, request.new_password
    )
    if not success:
        raise HTTPException(status_code=400, detail="Password reset failed")
    return {"message": "Password has been reset successfully"}


# verify_email
@router.post("/verify-email")
async def verify_email(
    request: EmailVerificationRequest,
    auth_service: AuthService = Depends(get_auth_service),
):
    success = await auth_service.verify_email(request.token)
    if not success:
        raise HTTPException(status_code=400, detail="Email verification failed")
    return {"message": "Email has been verified successfully"}
