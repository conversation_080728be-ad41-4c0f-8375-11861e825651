services:
  auth_service:
    build: .
    container_name: auth_service
    ports:
      - "8000:8000"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn auth_service.main:app --host 0.0.0.0 --port 8000

  inventory_service:
    build: .
    container_name: inventory_service
    ports:
      - "8001:8001"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn inventory_service.main:app --host 0.0.0.0 --port 8001

  products_service:
    build: .
    container_name: products_service
    ports:
      - "8002:8002"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn products_service.main:app --host 0.0.0.0 --port 8002

  orders_service:
    build: .
    container_name: orders_service
    ports:
      - "8003:8003"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn orders_service.main:app --host 0.0.0.0 --port 8003

  cart_service:
    build: .
    container_name: cart_service
    ports:
      - "8004:8004"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn cart_service.main:app --host 0.0.0.0 --port 8004

  payments_service:
    build: .
    container_name: payments_service
    ports:
      - "8005:8005"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn payments_service.main:app --host 0.0.0.0 --port 8005

  notifications_service:
    build: .
    container_name: notifications_service
    ports:
      - "8006:8006"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn notifications_service.main:app --host 0.0.0.0 --port 8006

  reporting_service:
    build: .
    container_name: reporting_service
    ports:
      - "8007:8007"
    depends_on:
      postgres_db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    command: uvicorn reporting_service.main:app --host 0.0.0.0 --port 8007

  postgres_db:
    image: postgres:15
    container_name: postgres_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: fehadan_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    container_name: redis
    ports:
      - "6379:6379"
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

volumes:
  postgres_data: