# crud operations for inventory
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
from typing import List, Optional
import datetime

from inventory_service.models import InventoryItem, Location
from shared.core.utils.logging import LoggingService


class InventoryRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.logger = LoggingService()

    async def create_inventory_item(self, item_data: dict) -> InventoryItem:
        """Create a new inventory item."""
        try:
            db_item = InventoryItem(**item_data)
            self.db.add(db_item)
            await self.db.commit()
            await self.db.refresh(db_item)
            return db_item
        except Exception as e:
            # rollback in case of error
            await self.db.rollback()
            self.logger.log(
                message="Error creating inventory item",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return None

    async def get_inventory_item_by_id(self, item_id: int) -> InventoryItem | None:
        """Retrieve an inventory item by ID."""
        try:
            result = await self.db.execute(select(InventoryItem).filter_by(id=item_id))
            item = result.scalar_one_or_none()
            return item
        except Exception as e:
            self.logger.log(
                message="Error retrieving inventory item",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return None

    async def get_inventory_item_by_product_and_location(
        self, product_id: int, location_id: int
    ) -> InventoryItem | None:
        """Retrieve an inventory item by product ID and location ID."""
        try:
            result = await self.db.execute(
                select(InventoryItem).filter_by(
                    product_id=product_id, location_id=location_id
                )
            )
            item = result.scalar_one_or_none()
            return item
        except Exception as e:
            self.logger.log(
                message="Error retrieving inventory item by product and location",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return None

    async def update_inventory_item(
        self, item_id: int, item_data: dict
    ) -> InventoryItem | None:
        """Update inventory item details."""
        try:

            await self.db.execute(
                update(InventoryItem)
                .where(InventoryItem.id == item_id)
                .values(**item_data)
            )
            await self.db.commit()

            # Get the updated item
            updated_item_response = await self.get_inventory_item_by_id(item_id)
            if updated_item_response:
                return updated_item_response
            else:
                return None
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error updating inventory item",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return None

    async def delete_inventory_item(self, item_id: int) -> bool:
        """Delete an inventory item."""
        try:
            await self.db.execute(
                delete(InventoryItem).where(InventoryItem.id == item_id)
            )
            await self.db.commit()
            return True
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error deleting inventory item",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return False

    async def list_inventory_items(
        self,
        product_id: Optional[int] = None,
        location_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[InventoryItem] | None:
        """List inventory items with optional filtering and pagination."""
        try:
            query = select(InventoryItem)

            # Apply filters if provided
            if product_id is not None:
                query = query.where(InventoryItem.product_id == product_id)
            if location_id is not None:
                query = query.where(InventoryItem.location_id == location_id)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await self.db.execute(query)
            items = result.scalars().all()
            return items
        except Exception as e:
            self.logger.log(
                message="Error listing inventory items",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return None

    async def get_location_by_id(self, location_id: int) -> Location | None:
        """Retrieve a location by ID."""
        try:
            result = await self.db.execute(select(Location).filter_by(id=location_id))
            location = result.scalar_one_or_none()
            return location
        except Exception as e:
            self.logger.log(
                message="Error retrieving location",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return None
