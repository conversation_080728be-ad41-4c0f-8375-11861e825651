"""
Inventory Item Service

This module handles all inventory item operations including creating, updating,
reading, and deleting inventory items.
"""

from typing import List, Optional
from fastapi import HTTPException

from inventory_service.repositories.inventory import InventoryRepository
from inventory_service.schemas import (
    InventoryItemCreate,
    InventoryItemResponse,
    InventoryItemUpdate,
)
from inventory_service.utils.validation import ValidationUtils
from shared.core.utils.logging import LoggingService


# Simple User type for microservice
class User:
    def __init__(self, email: str):
        self.email = email


class InventoryService:
    """Service class for managing inventory items."""

    def __init__(self, db):
        self.db = db
        self.inventory_repo = InventoryRepository(db)
        self.logger = LoggingService()
        self.validation_utils = ValidationUtils(db)

    async def create_stock_item(
        self,
        item_data: InventoryItemCreate,
        current_user: User,
        skip_product_validation: bool = False,
        skip_location_validation: bool = False,
    ) -> InventoryItemResponse:
        """
        Create a new inventory item.
        """
        try:
            print(f"Creating inventory item for product by user: {current_user.email}")

            # Handle both dict and Pydantic model inputs
            if isinstance(item_data, dict):
                product_id = item_data.get("product_id")
                location_id = item_data.get("location_id", 1)  # Default location
            else:
                product_id = item_data.product_id
                location_id = item_data.location_id

            # Get the product to verify it exists (skip for event-based creation)
            if (
                not skip_product_validation
                and not await self.validation_utils.validate_product_exists(product_id)
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"Product with ID {product_id} not found",
                )

            # Validate location exists (skip for event-based creation)
            if (
                not skip_location_validation
                and not await self.validation_utils.validate_location_exists(
                    location_id
                )
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"Location with ID {location_id} not found",
                )

            # Check for duplicate inventory item (same product at same location)
            existing_item = (
                await self.inventory_repo.get_inventory_item_by_product_and_location(
                    product_id, location_id
                )
            )
            if existing_item:
                raise HTTPException(
                    status_code=409,
                    detail="An inventory item for this product already exists at this location",
                )

            # Set created_by field and calculate available_quantity
            if isinstance(item_data, dict):
                item_dict = {**item_data, "created_by": current_user.id}
                # Ensure we have required fields with defaults
                item_dict.setdefault("location_id", location_id)
                item_dict.setdefault("product_id", product_id)
                item_dict.setdefault("quantity_on_hand", item_data.get("quantity", 0))
                item_dict.setdefault("reserved_quantity", 0)
                item_dict.setdefault("reorder_point", 10)
            else:
                item_dict = {**item_data.model_dump(), "created_by": current_user.id}

            # Calculate available_quantity
            quantity_on_hand = item_dict.get("quantity_on_hand", 0)
            reserved_quantity = item_dict.get("reserved_quantity", 0)
            item_dict["available_quantity"] = quantity_on_hand - reserved_quantity

            # Create inventory item
            result = await self.inventory_repo.create_inventory_item(item_dict)

            if not result:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to create inventory item",
                )

            return InventoryItemResponse.from_orm(result)

        except HTTPException:
            # Re-raise HTTPException without wrapping
            raise
        except Exception as e:
            self.logger.log(
                f"Unexpected error creating inventory item: {str(e)}",
                level="error",
                exception=e,
                app_name="inventory",
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while creating the inventory item",
            )

    async def get_inventory_item(self, item_id: int) -> InventoryItemResponse:
        """
        Get a single inventory item by ID.
        """
        try:
            self.logger.log(
                f"Getting inventory item with ID {item_id}",
                level="info",
                app_name="inventory",
            )

            result = await self.inventory_repo.get_inventory_item_by_id(item_id)

            if not result:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to get inventory item",
                )

            if not result:
                raise HTTPException(
                    status_code=404,
                    detail=f"Inventory item with ID {item_id} not found",
                )

            return InventoryItemResponse.from_orm(result)

        except HTTPException:
            # Re-raise HTTPException without wrapping
            raise
        except Exception as e:
            self.logger.log(
                f"Unexpected error getting inventory item {item_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="inventory",
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while getting the inventory item",
            )

    async def list_inventory_items(
        self,
        product_id: Optional[int] = None,
        location_id: Optional[int] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[InventoryItemResponse]:
        """
        List inventory items with optional filtering and pagination.
        """
        try:
            self.logger.log(
                f"Listing inventory items with filters: product_id={product_id}, location_id={location_id}, limit={limit}, offset={offset}",
                level="info",
                app_name="inventory",
            )

            result = await self.inventory_repo.list_inventory_items(
                product_id=product_id,
                location_id=location_id,
                skip=offset,
                limit=limit,
            )

            if result is None:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to list inventory items",
                )

            items = [InventoryItemResponse.from_orm(item) for item in result]
            return items

        except HTTPException:
            # Re-raise HTTPException without wrapping
            raise
        except Exception as e:
            self.logger.log(
                f"Unexpected error listing inventory items: {str(e)}",
                level="error",
                exception=e,
                app_name="inventory",
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while listing inventory items",
            )

    async def update_inventory_item(
        self, item_id: int, update_data: InventoryItemUpdate, current_user: User
    ) -> InventoryItemResponse:
        """
        Update an inventory item.
        """
        try:
            self.logger.log(
                f"Updating inventory item {item_id} by user: {current_user.email}",
                level="info",
                app_name="inventory",
            )

            # Check if item exists
            existing_item = await self.inventory_repo.get_inventory_item_by_id(item_id)
            if not existing_item:
                raise HTTPException(
                    status_code=404,
                    detail=f"Inventory item with ID {item_id} not found",
                )

            # Filter out None values and prepare update data
            update_dict = {
                k: v for k, v in update_data.model_dump().items() if v is not None
            }
            update_dict["updated_by"] = current_user.id

            result = await self.inventory_repo.update_inventory_item(
                item_id, update_dict
            )

            if not result:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to update inventory item",
                )

            return InventoryItemResponse.from_orm(result)

        except HTTPException:
            # Re-raise HTTPException without wrapping
            raise
        except Exception as e:
            self.logger.log(
                f"Unexpected error updating inventory item {item_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="inventory",
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while updating the inventory item",
            )

    async def delete_inventory_item(self, item_id: int, current_user: User) -> None:
        """
        Delete an inventory item.
        """
        try:
            self.logger.log(
                f"Deleting inventory item {item_id} by user: {current_user.email}",
                level="info",
                app_name="inventory",
            )

            # Check if item exists
            existing_item = await self.inventory_repo.get_inventory_item_by_id(item_id)
            if not existing_item:
                raise HTTPException(
                    status_code=404,
                    detail=f"Inventory item with ID {item_id} not found",
                )

            result = await self.inventory_repo.delete_inventory_item(item_id)

            if not result:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to delete inventory item",
                )

            # Return None for 204 No Content

        except HTTPException:
            # Re-raise HTTPException without wrapping
            raise
        except Exception as e:
            self.logger.log(
                f"Unexpected error deleting inventory item {item_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="inventory",
            )
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while deleting the inventory item",
            )
