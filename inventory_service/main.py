from fastapi import FastAP<PERSON>, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from inventory_service.routes import router as inventory_router
from inventory_service.event_handlers import start_event_listener
from shared.config import settings
from shared.database import engine, Base
from datetime import datetime
from sqlalchemy import text
import asyncio
import logging

logger = logging.getLogger(__name__)

app = FastAPI(title="Inventory Microservice")

# CORS setup (adjust origins as needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(inventory_router, prefix="/inventory", tags=["inventory"])


# Create database tables
@app.on_event("startup")
async def startup():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # Start the event listener in the background
    asyncio.create_task(start_event_listener())
    logger.info("🚀 Inventory service started with event listener")


# Health check endpoint
@app.get("/")
async def health_check():
    """Health check endpoint that verifies service and database connectivity."""
    timestamp = datetime.utcnow().isoformat() + "Z"

    try:
        # Test database connection
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

        return {
            "status": "healthy",
            "service": "inventory-microservice",
            "version": "1.0.0",
            "database": "connected",
            "timestamp": timestamp,
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "inventory-microservice",
            "version": "1.0.0",
            "database": "disconnected",
            "error": str(e),
            "timestamp": timestamp,
        }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8001)
