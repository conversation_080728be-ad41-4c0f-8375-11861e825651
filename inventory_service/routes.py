from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_db
from inventory_service.services.inventory_item import InventoryService, User
from inventory_service.schemas import (
    InventoryItemCreate,
    InventoryItemResponse,
    InventoryItemUpdate,
)

router = APIRouter()


# Simple dependency to get current user for microservice
# In a full implementation, this would verify JWT tokens
async def get_current_user() -> User:
    """
    Simple user dependency for microservice.
    In production, this would validate JWT tokens and fetch user data.
    """
    return User(email="<EMAIL>")


def get_inventory_service(db: AsyncSession = Depends(get_db)) -> InventoryService:
    """Dependency to get inventory service instance."""
    return InventoryService(db)


@router.get(
    "/items/{item_id}",
    response_model=InventoryItemResponse,
    summary="Get inventory item",
    description="Retrieve a single inventory item by ID",
)
async def get_inventory_item(
    item_id: int,
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> InventoryItemResponse:
    """
    Get a single inventory item by ID.

    - **item_id**: The ID of the inventory item to retrieve
    """
    return await inventory_service.get_inventory_item(item_id)


@router.get(
    "/items",
    response_model=List[InventoryItemResponse],
    summary="List inventory items",
    description="List inventory items with optional filtering and pagination",
)
async def list_inventory_items(
    product_id: Optional[int] = Query(None, description="Filter by product ID"),
    location_id: Optional[int] = Query(None, description="Filter by location ID"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of items to return"
    ),
    offset: int = Query(0, ge=0, description="Number of items to skip for pagination"),
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> List[InventoryItemResponse]:
    """
    List inventory items with optional filtering and pagination.

    - **product_id**: Optional filter by product ID
    - **location_id**: Optional filter by location ID
    - **limit**: Maximum number of items to return (1-1000)
    - **offset**: Number of items to skip for pagination
    """
    return await inventory_service.list_inventory_items(
        product_id=product_id,
        location_id=location_id,
        limit=limit,
        offset=offset,
    )


@router.post(
    "/items",
    response_model=InventoryItemResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create inventory item",
    description="Create a new inventory item",
)
async def create_inventory_item(
    item_data: InventoryItemCreate,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_user),
) -> InventoryItemResponse:
    """
    Create a new inventory item.

    - **product_id**: ID of the product
    - **location_id**: ID of the location where the product is stored
    - **quantity_on_hand**: Current quantity available
    - **reserved_quantity**: Quantity reserved for orders
    - **reorder_point**: Minimum quantity before reordering
    - **max_stock_level**: Maximum stock level (optional)
    - **batch_number**: Batch number (optional)
    - **lot_number**: Lot number (optional)
    - **received_date**: Date when inventory was received (optional)
    - **expiration_date**: Expiration date (optional)
    """
    return await inventory_service.create_stock_item(item_data, current_user)


@router.put(
    "/items/{item_id}",
    response_model=InventoryItemResponse,
    summary="Update inventory item",
    description="Update an existing inventory item",
)
async def update_inventory_item(
    item_id: int,
    update_data: InventoryItemUpdate,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_user),
) -> InventoryItemResponse:
    """
    Update an existing inventory item.

    - **item_id**: The ID of the inventory item to update
    - **quantity_on_hand**: Updated quantity available
    - **reserved_quantity**: Updated quantity reserved for orders
    - **reorder_point**: Updated minimum quantity before reordering
    - **max_stock_level**: Updated maximum stock level (optional)
    - **batch_number**: Updated batch number (optional)
    - **lot_number**: Updated lot number (optional)
    - **received_date**: Updated date when inventory was received (optional)
    - **expiration_date**: Updated expiration date (optional)
    - **is_active**: Whether the inventory item is active (optional)
    """
    return await inventory_service.update_inventory_item(
        item_id, update_data, current_user
    )


@router.delete(
    "/items/{item_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete inventory item",
    description="Delete an inventory item",
)
async def delete_inventory_item(
    item_id: int,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_user),
) -> None:
    """
    Delete an inventory item.

    - **item_id**: The ID of the inventory item to delete
    """
    await inventory_service.delete_inventory_item(item_id, current_user)


# Additional utility endpoints


@router.get(
    "/items/{item_id}/availability",
    summary="Check item availability",
    description="Check available quantity for an inventory item",
)
async def check_item_availability(
    item_id: int,
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> dict:
    """
    Check available quantity for an inventory item.

    - **item_id**: The ID of the inventory item to check
    """
    item = await inventory_service.get_inventory_item(item_id)
    return {
        "item_id": item.id,
        "product_id": item.product_id,
        "location_id": item.location_id,
        "quantity_on_hand": item.quantity_on_hand,
        "reserved_quantity": item.reserved_quantity,
        "available_quantity": item.available_quantity,
        "is_available": item.available_quantity > 0,
        "low_stock": item.quantity_on_hand <= item.reorder_point,
    }


@router.get(
    "/items/low-stock",
    response_model=List[InventoryItemResponse],
    summary="Get low stock items",
    description="Get inventory items that are at or below reorder point",
)
async def get_low_stock_items(
    location_id: Optional[int] = Query(None, description="Filter by location ID"),
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> List[InventoryItemResponse]:
    """
    Get inventory items that are at or below reorder point.

    - **location_id**: Optional filter by location ID
    """
    # This would need to be implemented in the service layer
    # For now, we'll get all items and filter client-side
    all_items = await inventory_service.list_inventory_items(
        location_id=location_id, limit=1000
    )
    return [item for item in all_items if item.quantity_on_hand <= item.reorder_point]
