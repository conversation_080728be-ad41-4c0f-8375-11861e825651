"""
Event handlers for inventory service.

Listens to events from other services and reacts accordingly.
"""

import sys
import os
import time
import logging
import json
import asyncio

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.database import get_db
from shared.events import Event, PRODUCT_CREATED, PRODUCT_UPDATED, PRODUCT_DELETED
from shared.redis_client import subscribe_to_events
from inventory_service.schemas import InventoryItemCreate
from inventory_service.services.inventory_item import InventoryService

logger = logging.getLogger(__name__)


class InventoryEventHandler:
    """Event handler for inventory-related events."""

    def __init__(self):
        self.inventory_service = None
        self._system_user = None  # Cache for system user

    async def _get_system_user(self, db):
        """Get or create a system user for automated operations."""
        if self._system_user is None:
            # Create a simple system user object for event operations
            class SystemUser:
                def __init__(self):
                    self.id = -1  # System user ID
                    self.email = "<EMAIL>"
                    self.username = "system"
                    self.full_name = "System User"
                    self.role = "admin"
                    self.is_verified = True
                    self.is_active = True

            self._system_user = SystemUser()

        return self._system_user

    async def _get_inventory_service(self, db):
        """Get or create inventory service instance."""
        if self.inventory_service is None:
            self.inventory_service = InventoryService(db)
        return self.inventory_service

    async def handle_product_created(self, event_data: dict):
        """Handle PRODUCT_CREATED event by creating inventory item."""
        try:
            product_id = event_data.get("product_id")
            product_name = event_data.get("product_name")
            stock_quantity = event_data.get("stock_quantity", 0)
            location_id = event_data.get("location_id", 1)
            reorder_point = event_data.get("reorder_point", 10)
            created_by = event_data.get("created_by", "system")

            logger.info(
                f"🎉 Product {product_id} ({product_name}) created, creating inventory item"
            )

            # Get database session and services
            async for db in get_db():
                system_user = await self._get_system_user(db)
                inventory_service = await self._get_inventory_service(db)

                # Prepare inventory item data
                inventory_data = InventoryItemCreate(
                    product_id=product_id,
                    location_id=location_id,
                    quantity_on_hand=stock_quantity,
                    reserved_quantity=0,
                    reorder_point=reorder_point,
                    batch_number=None,
                    lot_number=None,
                )

                # Create the inventory item (skip validations for event-based creation)
                inventory_item = await inventory_service.create_stock_item(
                    inventory_data,
                    system_user,
                    skip_product_validation=True,
                    skip_location_validation=True,
                )

                if inventory_item:
                    logger.info(
                        f"✅ Successfully created inventory item {inventory_item.id} for product {product_id}"
                    )
                else:
                    logger.error(
                        f"❌ Failed to create inventory item for product {product_id}"
                    )

                break  # Exit the async generator

        except Exception as e:
            logger.error(
                f"Error handling PRODUCT_CREATED event: {str(e)}", exc_info=True
            )

    async def handle_product_updated(self, event_data: dict):
        """Handle PRODUCT_UPDATED event."""
        try:
            product_id = event_data.get("product_id")
            updated_fields = event_data.get("updated_fields", [])

            logger.info(f"📝 Product {product_id} updated, fields: {updated_fields}")

        except Exception as e:
            logger.error(f"Error handling PRODUCT_UPDATED event: {str(e)}")

    async def handle_product_deleted(self, event_data: dict):
        """Handle PRODUCT_DELETED event by removing related inventory."""
        try:
            product_id = event_data.get("product_id")

            logger.info(f"🗑️ Product {product_id} deleted, removing inventory")

            # Get database session and services
            async for db in get_db():
                system_user = await self._get_system_user(db)
                inventory_service = await self._get_inventory_service(db)

                logger.info(
                    f"Would deactivate inventory items for product {product_id}"
                )

                break  # Exit the async generator

        except Exception as e:
            logger.error(f"Error handling PRODUCT_DELETED event: {str(e)}")


async def start_event_listener():
    """Start listening for events and handle them."""
    logger.info("🚀 Starting Inventory Event Listener...")

    handler = InventoryEventHandler()

    # Subscribe to product_events channel where products service publishes
    pubsub = await subscribe_to_events("product_events")
    if not pubsub:
        logger.error("Failed to subscribe to product_events")
        return

    logger.info("👂 Listening for product events...")

    try:
        async for message in pubsub.listen():
            if message["type"] == "message":
                try:
                    event_data = json.loads(message["data"])
                    event_type = event_data.get("type")
                    payload = event_data.get("data", {})

                    logger.info(f"📢 Received event: {event_type}")

                    # Route events to appropriate handlers
                    if event_type == PRODUCT_CREATED:
                        await handler.handle_product_created(payload)
                    elif event_type == PRODUCT_UPDATED:
                        await handler.handle_product_updated(payload)
                    elif event_type == PRODUCT_DELETED:
                        await handler.handle_product_deleted(payload)
                    else:
                        logger.debug(f"Ignoring event type: {event_type}")

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse event data: {message['data']}")
                except Exception as e:
                    logger.error(f"Error handling event: {str(e)}")

    except KeyboardInterrupt:
        logger.info("🛑 Shutting down Inventory Event Listener...")
    finally:
        await pubsub.aclose()


if __name__ == "__main__":
    # Run the event listener
    asyncio.run(start_event_listener())
