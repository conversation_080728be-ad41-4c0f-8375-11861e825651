"""
Test factory for product model using factory-boy.
"""

import factory
from factory.alchemy import SQLAlchemyModelFactory

import factory
from factory import Faker, Sequence, LazyFunction, SubFactory, LazyAttribute
from factory.alchemy import SQLAlchemyModelFactory
from sqlalchemy.ext.asyncio import AsyncSession
import datetime
from decimal import Decimal

from products_service.model import *


class ProductFactory(SQLAlchemyModelFactory):
    """Factory for creating Product instances for testing."""

    class Meta:
        model = Product
        sqlalchemy_session_persistence = "commit"

    name = Faker("word")
    description = Faker("text", max_nb_chars=200)
    price = Faker("pydecimal", left_digits=3, right_digits=2, positive=True)
    currency = "USD"
    in_stock = True
    stock_quantity = Faker("random_int", min=0, max=1000)
    unity_of_measure = "lb"
    tags = factory.LazyFunction(lambda: ["fresh", "premium"])
    images = factory.LazyFunction(
        lambda: [f"https://example.com/image{i}.jpg" for i in range(1, 4)]
    )

    # Timestamps
    created_at = LazyFunction(lambda: datetime.datetime.now(datetime.timezone.utc))
    updated_at = LazyFunction(lambda: datetime.datetime.now(datetime.timezone.utc))


class ChickenProductFactory(ProductFactory):
    """Factory for chicken products."""

    name = Sequence(lambda n: f"Premium Chicken Breast {n}")
    description = "Fresh, boneless chicken breast perfect for grilling or baking"
    price = Faker(
        "pydecimal",
        left_digits=2,
        right_digits=2,
        positive=True,
        min_value=8,
        max_value=15,
    )
    tags = ["fresh", "boneless", "premium"]


class BeefProductFactory(ProductFactory):
    """Factory for beef products."""

    name = Sequence(lambda n: f"Prime Ribeye Steak {n}")
    description = "Premium cut ribeye steak with excellent marbling"
    price = Faker(
        "pydecimal",
        left_digits=2,
        right_digits=2,
        positive=True,
        min_value=20,
        max_value=40,
    )
    tags = ["fresh", "prime", "premium"]


class PorkProductFactory(ProductFactory):
    """Factory for pork products."""

    name = Sequence(lambda n: f"Pork Tenderloin {n}")
    description = "Lean and tender pork tenderloin, perfect for roasting"
    price = Faker(
        "pydecimal",
        left_digits=2,
        right_digits=2,
        positive=True,
        min_value=12,
        max_value=18,
    )
    tags = ["fresh", "lean", "tender"]


class GoatProductFactory(ProductFactory):
    """Factory for goat products."""

    name = Sequence(lambda n: f"Goat Leg {n}")
    description = "Fresh goat leg, excellent for traditional dishes"
    price = Faker(
        "pydecimal",
        left_digits=2,
        right_digits=2,
        positive=True,
        min_value=15,
        max_value=25,
    )
    tags = ["fresh", "specialty", "traditional"]


class MeatDetailsFactory(SQLAlchemyModelFactory):
    """Factory for creating MeatDetails instances for testing."""

    class Meta:
        model = MeatDetails
        sqlalchemy_session_persistence = "commit"

    product = SubFactory(ProductFactory)
    animal_type = AnimalType.CHICKEN
    cut_type = "breast"
    grade = "A"
    processing_type = ProcessingType.FRESH
    preparation = PreparationType.BONELESS
    packaging_type = PackagingType.TRAY
    origin = Faker("country")
    feed_type = "grain_fed"
    certifications = factory.LazyFunction(lambda: ["USDA_inspected", "hormone_free"])
    lean_ratio = None

    # Storage and cooking information
    shelf_life_fresh = "3-5 days refrigerated"
    shelf_life_frozen = "6-9 months frozen"
    storage_instructions = "Keep refrigerated at 40°F or below"
    cooking_instructions = "Cook to internal temperature of 165°F"


class ChickenDetailsFactory(MeatDetailsFactory):
    """Factory for chicken meat details."""

    product = SubFactory(ChickenProductFactory)
    animal_type = AnimalType.CHICKEN
    cut_type = Faker(
        "random_element", elements=["breast", "thigh", "wing", "drumstick", "whole"]
    )
    grade = "A"
    processing_type = ProcessingType.FRESH
    preparation = Faker(
        "random_element",
        elements=[
            PreparationType.BONELESS,
            PreparationType.BONELESS_SKINLESS,
            PreparationType.SKIN_ON,
        ],
    )
    feed_type = Faker("random_element", elements=["grain_fed", "free_range", "organic"])
    cooking_instructions = "Cook to internal temperature of 165°F"


class BeefDetailsFactory(MeatDetailsFactory):
    """Factory for beef meat details."""

    product = SubFactory(BeefProductFactory)
    animal_type = AnimalType.BEEF
    cut_type = Faker(
        "random_element",
        elements=["ribeye", "sirloin", "tenderloin", "strip", "t-bone"],
    )
    grade = Faker("random_element", elements=["Prime", "Choice", "Select"])
    processing_type = ProcessingType.FRESH
    preparation = PreparationType.BONE_IN
    feed_type = Faker("random_element", elements=["grass_fed", "grain_fed", "organic"])
    cooking_instructions = "Cook to desired doneness: 135°F for medium-rare"


class PorkDetailsFactory(MeatDetailsFactory):
    """Factory for pork meat details."""

    product = SubFactory(PorkProductFactory)
    animal_type = AnimalType.PORK
    cut_type = Faker(
        "random_element", elements=["tenderloin", "chop", "shoulder", "belly", "ribs"]
    )
    grade = "AA"
    processing_type = ProcessingType.FRESH
    preparation = PreparationType.BONELESS
    feed_type = "grain_fed"
    cooking_instructions = "Cook to internal temperature of 145°F"


class GoatDetailsFactory(MeatDetailsFactory):
    """Factory for goat meat details."""

    product = SubFactory(GoatProductFactory)
    animal_type = AnimalType.GOAT
    cut_type = Faker("random_element", elements=["leg", "shoulder", "chops", "ground"])
    grade = "A"
    processing_type = ProcessingType.FRESH
    preparation = PreparationType.BONE_IN
    feed_type = "grass_fed"
    cooking_instructions = "Cook to internal temperature of 145°F"


class GroundMeatDetailsFactory(MeatDetailsFactory):
    """Factory for ground meat details."""

    preparation = PreparationType.GROUND
    lean_ratio = Faker("random_element", elements=["80/20", "85/15", "90/10", "93/7"])
    cooking_instructions = "Cook to internal temperature of 160°F"


class GroundBeefDetailsFactory(GroundMeatDetailsFactory):
    """Factory for ground beef details."""

    product = SubFactory(BeefProductFactory)
    animal_type = AnimalType.BEEF
    cut_type = "ground"
    grade = "Choice"


class NutritionalInfoFactory(SQLAlchemyModelFactory):
    """Factory for creating NutritionalInfo instances for testing."""

    class Meta:
        model = NutritionalInfo
        sqlalchemy_session_persistence = "commit"

    product = SubFactory(ProductFactory)
    serving_size = "4 oz (113g)"
    calories = Faker("random_int", min=150, max=350)
    protein = LazyAttribute(
        lambda obj: f"{Faker('random_int', min=20, max=40).generate()}g"
    )
    fat = LazyAttribute(lambda obj: f"{Faker('random_int', min=2, max=15).generate()}g")
    saturated_fat = LazyAttribute(
        lambda obj: f"{Faker('random_int', min=1, max=5).generate()}g"
    )
    cholesterol = LazyAttribute(
        lambda obj: f"{Faker('random_int', min=60, max=100).generate()}mg"
    )
    sodium = LazyAttribute(
        lambda obj: f"{Faker('random_int', min=50, max=150).generate()}mg"
    )
    carbohydrates = "0g"
    fiber = "0g"
    sugar = "0g"


class ChickenNutritionalInfoFactory(NutritionalInfoFactory):
    """Factory for chicken nutritional info."""

    product = SubFactory(ChickenProductFactory)
    calories = Faker("random_int", min=170, max=210)
    protein = "31g"
    fat = "4g"
    saturated_fat = "1g"
    cholesterol = "85mg"
    sodium = "74mg"


class BeefNutritionalInfoFactory(NutritionalInfoFactory):
    """Factory for beef nutritional info."""

    product = SubFactory(BeefProductFactory)
    calories = Faker("random_int", min=250, max=350)
    protein = "25g"
    fat = "15g"
    saturated_fat = "6g"
    cholesterol = "95mg"
    sodium = "65mg"


class PackagingFactory(SQLAlchemyModelFactory):
    """Factory for creating Packaging instances for testing."""

    class Meta:
        model = Packaging
        sqlalchemy_session_persistence = "commit"

    product = SubFactory(ProductFactory)
    weight = LazyAttribute(
        lambda obj: f"{Faker('pydecimal', left_digits=1, right_digits=2, positive=True, min_value=0.5, max_value=5).generate()} lb"
    )
    package_size = "individual"
    packaged_date = LazyFunction(
        lambda: datetime.datetime.utcnow()
        - datetime.timedelta(days=Faker("random_int", min=0, max=2).generate())
    )
    expiration_date = LazyFunction(
        lambda: datetime.datetime.utcnow()
        + datetime.timedelta(days=Faker("random_int", min=3, max=7).generate())
    )
    batch_number = Sequence(lambda n: f"BATCH-{n:06d}")


class FreshPackagingFactory(PackagingFactory):
    """Factory for fresh product packaging."""

    package_size = "individual"
    expiration_date = LazyFunction(
        lambda: datetime.datetime.utcnow()
        + datetime.timedelta(days=Faker("random_int", min=3, max=7).generate())
    )


class FrozenPackagingFactory(PackagingFactory):
    """Factory for frozen product packaging."""

    package_size = "family_pack"
    expiration_date = LazyFunction(
        lambda: datetime.datetime.utcnow()
        + datetime.timedelta(days=Faker("random_int", min=180, max=365).generate())
    )


class BulkPackagingFactory(PackagingFactory):
    """Factory for bulk packaging."""

    weight = LazyAttribute(
        lambda obj: f"{Faker('pydecimal', left_digits=2, right_digits=2, positive=True, min_value=10, max_value=50).generate()} lb"
    )
    package_size = "bulk"


# Complete product factories with all relationships
class CompleteChickenProductFactory(ProductFactory):
    """Factory for complete chicken products with all relationships."""

    name = Sequence(lambda n: f"Premium Chicken Breast {n}")
    description = "Fresh, boneless chicken breast perfect for grilling or baking"
    price = Faker(
        "pydecimal",
        left_digits=2,
        right_digits=2,
        positive=True,
        min_value=8,
        max_value=15,
    )
    tags = ["fresh", "boneless", "premium"]

    meat_details = SubFactory(ChickenDetailsFactory)
    nutritional_info = SubFactory(ChickenNutritionalInfoFactory)
    packaging = SubFactory(FreshPackagingFactory)


class CompleteBeefProductFactory(ProductFactory):
    """Factory for complete beef products with all relationships."""

    name = Sequence(lambda n: f"Prime Ribeye Steak {n}")
    description = "Premium cut ribeye steak with excellent marbling"
    price = Faker(
        "pydecimal",
        left_digits=2,
        right_digits=2,
        positive=True,
        min_value=20,
        max_value=40,
    )
    tags = ["fresh", "prime", "premium"]

    meat_details = SubFactory(BeefDetailsFactory)
    nutritional_info = SubFactory(BeefNutritionalInfoFactory)
    packaging = SubFactory(FreshPackagingFactory)


# Async-compatible factory functions
async def create_product(session: AsyncSession, **kwargs) -> Product:
    """Create a product using the factory with async session support."""
    ProductFactory._meta.sqlalchemy_session = session
    product = ProductFactory(**kwargs)
    session.add(product)
    await session.commit()
    await session.refresh(product)
    return product


async def create_chicken_product(session: AsyncSession, **kwargs) -> Product:
    """Create a chicken product using the factory with async session support."""
    ChickenProductFactory._meta.sqlalchemy_session = session
    product = ChickenProductFactory(**kwargs)
    session.add(product)
    await session.commit()
    await session.refresh(product)
    return product


async def create_beef_product(session: AsyncSession, **kwargs) -> Product:
    """Create a beef product using the factory with async session support."""
    BeefProductFactory._meta.sqlalchemy_session = session
    product = BeefProductFactory(**kwargs)
    session.add(product)
    await session.commit()
    await session.refresh(product)
    return product


async def create_pork_product(session: AsyncSession, **kwargs) -> Product:
    """Create a pork product using the factory with async session support."""
    PorkProductFactory._meta.sqlalchemy_session = session
    product = PorkProductFactory(**kwargs)
    session.add(product)
    await session.commit()
    await session.refresh(product)
    return product


async def create_goat_product(session: AsyncSession, **kwargs) -> Product:
    """Create a goat product using the factory with async session support."""
    GoatProductFactory._meta.sqlalchemy_session = session
    product = GoatProductFactory(**kwargs)
    session.add(product)
    await session.commit()
    await session.refresh(product)
    return product


async def create_meat_details(session: AsyncSession, **kwargs) -> MeatDetails:
    """Create meat details using the factory with async session support."""
    MeatDetailsFactory._meta.sqlalchemy_session = session
    details = MeatDetailsFactory(**kwargs)
    session.add(details)
    await session.commit()
    await session.refresh(details)
    return details


async def create_chicken_details(session: AsyncSession, **kwargs) -> MeatDetails:
    """Create chicken meat details using the factory with async session support."""
    ChickenDetailsFactory._meta.sqlalchemy_session = session
    details = ChickenDetailsFactory(**kwargs)
    session.add(details)
    await session.commit()
    await session.refresh(details)
    return details


async def create_beef_details(session: AsyncSession, **kwargs) -> MeatDetails:
    """Create beef meat details using the factory with async session support."""
    BeefDetailsFactory._meta.sqlalchemy_session = session
    details = BeefDetailsFactory(**kwargs)
    session.add(details)
    await session.commit()
    await session.refresh(details)
    return details


async def create_nutritional_info(session: AsyncSession, **kwargs) -> NutritionalInfo:
    """Create nutritional info using the factory with async session support."""
    NutritionalInfoFactory._meta.sqlalchemy_session = session
    nutrition = NutritionalInfoFactory(**kwargs)
    session.add(nutrition)
    await session.commit()
    await session.refresh(nutrition)
    return nutrition


async def create_packaging(session: AsyncSession, **kwargs) -> Packaging:
    """Create packaging using the factory with async session support."""
    PackagingFactory._meta.sqlalchemy_session = session
    packaging = PackagingFactory(**kwargs)
    session.add(packaging)
    await session.commit()
    await session.refresh(packaging)
    return packaging


async def create_complete_chicken_product(session: AsyncSession, **kwargs) -> Product:
    """Create a complete chicken product with all relationships."""
    CompleteChickenProductFactory._meta.sqlalchemy_session = session
    product = CompleteChickenProductFactory(**kwargs)
    session.add(product)
    await session.commit()
    await session.refresh(product)
    return product


async def create_complete_beef_product(session: AsyncSession, **kwargs) -> Product:
    """Create a complete beef product with all relationships."""
    CompleteBeefProductFactory._meta.sqlalchemy_session = session
    product = CompleteBeefProductFactory(**kwargs)
    session.add(product)
    await session.commit()
    await session.refresh(product)
    return product


# Utility functions for tests
def build_product(**kwargs) -> Product:
    """Build a product instance without saving to database."""
    return ProductFactory.build(**kwargs)


def build_chicken_product(**kwargs) -> Product:
    """Build a chicken product instance without saving to database."""
    return ChickenProductFactory.build(**kwargs)


def build_beef_product(**kwargs) -> Product:
    """Build a beef product instance without saving to database."""
    return BeefProductFactory.build(**kwargs)


def build_meat_details(**kwargs) -> MeatDetails:
    """Build meat details instance without saving to database."""
    return MeatDetailsFactory.build(**kwargs)


def build_nutritional_info(**kwargs) -> NutritionalInfo:
    """Build nutritional info instance without saving to database."""
    return NutritionalInfoFactory.build(**kwargs)


def build_packaging(**kwargs) -> Packaging:
    """Build packaging instance without saving to database."""
    return PackagingFactory.build(**kwargs)


# Usage Examples and Documentation
"""
Product Factory Usage Examples:

Basic Usage:
-----------

# Create basic products
product = await create_product(session, name="Test Product", price=10.99)
chicken = await create_chicken_product(session)
beef = await create_beef_product(session)

# Create with details
meat_details = await create_chicken_details(session, product=chicken)
nutrition = await create_nutritional_info(session, product=chicken)
packaging = await create_packaging(session, product=chicken)

# Create complete products
complete_chicken = await create_complete_chicken_product(session)
complete_beef = await create_complete_beef_product(session)

Advanced Usage:
--------------

# Create custom products
premium_steak = await create_beef_product(
    session,
    name="Wagyu Ribeye",
    price=Decimal("89.99"),
    description="Premium wagyu ribeye steak"
)

# Create with specific meat details
wagyu_details = await create_beef_details(
    session,
    product=premium_steak,
    grade="Prime",
    feed_type="grass_fed",
    certifications=["wagyu_certified", "organic"]
)

# Create ground meat
ground_beef = await create_product(session, name="Ground Beef 80/20")
ground_details = await create_meat_details(
    session,
    product=ground_beef,
    animal_type=AnimalType.BEEF,
    preparation=PreparationType.GROUND,
    lean_ratio="80/20"
)

Build Without Saving:
--------------------

# Build instances for testing without database persistence
product = build_product(name="Test Product", price=15.99)
chicken = build_chicken_product()
details = build_meat_details(animal_type=AnimalType.CHICKEN)
"""
