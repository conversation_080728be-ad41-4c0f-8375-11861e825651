"""
Test suite for the Product repository using the ProductFactory.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from decimal import Decimal
from fastapi import HTTPException

from products_service.repositories.product import ProductRepository
from products_service.schemas import ProductCreate, ProductUpdate
from products_service.tests.factory import ProductFactory


@pytest.fixture
def product_repo(test_db: AsyncSession) -> ProductRepository:
    return ProductRepository(test_db)


@pytest.mark.asyncio
async def test_create_product(product_repo: ProductRepository):
    product_data = ProductCreate(
        name="Test Product",
        description="A product for testing",
        price=Decimal("19.99"),
        currency="USD",
        in_stock=True,
        stock_quantity=100,
    )

    response = await product_repo.create_product(product_data)
    assert response.name == product_data.name
    assert response.price == product_data.price
    assert response.id is not None


@pytest.mark.asyncio
async def test_get_product_by_id(
    product_repo: ProductRepository, test_db: AsyncSession
):
    ProductFactory._meta.sqlalchemy_session = test_db
    product = ProductFactory.build()
    test_db.add(product)
    await test_db.commit()
    await test_db.refresh(product)

    response = await product_repo.get_product_by_id(product.id)
    assert response.id == product.id


@pytest.mark.asyncio
async def test_update_product(product_repo: ProductRepository, test_db: AsyncSession):
    ProductFactory._meta.sqlalchemy_session = test_db
    product = ProductFactory.build()
    test_db.add(product)
    await test_db.commit()
    await test_db.refresh(product)

    update_data = ProductUpdate(price=Decimal("29.99"), stock_quantity=50)
    response = await product_repo.update_product(product.id, update_data)
    assert float(response.price) == 29.99
    assert response.stock_quantity == 50


@pytest.mark.asyncio
async def test_delete_product(product_repo: ProductRepository, test_db: AsyncSession):
    ProductFactory._meta.sqlalchemy_session = test_db
    product = ProductFactory.build()
    test_db.add(product)
    await test_db.commit()
    await test_db.refresh(product)

    # The delete method doesn't return anything meaningful in this implementation
    # So we just test that it doesn't raise an exception
    await product_repo.delete_product(product.id)

    # Verify the product is deleted by checking if get_product_by_id raises 404
    with pytest.raises(Exception):  # Should raise HTTPException with 404
        await product_repo.get_product_by_id(product.id)
