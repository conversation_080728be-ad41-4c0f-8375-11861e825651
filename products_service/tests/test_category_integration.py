"""
Test suite for Category model integration with Product model.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal

from products_service.services.product import ProductService
from products_service.repositories.category import CategoryRepository
from products_service.schemas import ProductCreate


@pytest.fixture
def product_service(test_db: AsyncSession) -> ProductService:
    return ProductService(test_db)


@pytest.fixture
def category_repo(test_db: AsyncSession) -> CategoryRepository:
    return CategoryRepository(test_db)


@pytest.mark.asyncio
async def test_create_product_with_categories(product_service: ProductService):
    """Test creating a product with categories."""
    product_data = ProductCreate(
        name="Test Product with Categories",
        description="A product for testing categories",
        price=Decimal("19.99"),
        currency="USD",
        in_stock=True,
        stock_quantity=100,
        categories=["meat", "beef", "premium"]
    )

    current_user = {"id": 1, "email": "<EMAIL>"}
    response = await product_service.create_product(current_user, product_data)
    assert response.name == product_data.name
    assert response.price == product_data.price
    assert response.id is not None
    assert len(response.categories) == 3

    category_names = [cat.name for cat in response.categories]
    assert "meat" in category_names
    assert "beef" in category_names
    assert "premium" in category_names


@pytest.mark.asyncio
async def test_create_category_directly(category_repo: CategoryRepository):
    """Test creating a category directly."""
    category = await category_repo.create_category("test_category", "A test category")
    assert category.name == "test_category"
    assert category.description == "A test category"
    assert category.is_active is True
    assert category.id is not None


@pytest.mark.asyncio
async def test_get_or_create_categories(category_repo: CategoryRepository):
    """Test get_or_create_categories functionality."""
    # Create one category first
    await category_repo.create_category("existing_category", "Already exists")
    
    # Test with mix of existing and new categories
    categories = await category_repo.get_or_create_categories([
        "existing_category", 
        "new_category1", 
        "new_category2"
    ])
    
    assert len(categories) == 3
    category_names = [cat.name for cat in categories]
    assert "existing_category" in category_names
    assert "new_category1" in category_names
    assert "new_category2" in category_names


@pytest.mark.asyncio
async def test_update_product_categories(product_service: ProductService):
    """Test updating product categories."""
    product_data = ProductCreate(
        name="Test Product for Update",
        description="A product for testing category updates",
        price=Decimal("29.99"),
        categories=["meat", "chicken"]
    )

    current_user = {"id": 1, "email": "<EMAIL>"}
    product = await product_service.create_product(current_user, product_data)
    assert len(product.categories) == 2

    update_data = {
        "categories": ["meat", "poultry", "premium"]
    }
    updated_product = await product_service.update_product(product.id, update_data)

    assert len(updated_product.categories) == 3
    category_names = [cat.name for cat in updated_product.categories]
    assert "meat" in category_names
    assert "poultry" in category_names
    assert "premium" in category_names
    assert "chicken" not in category_names


@pytest.mark.asyncio
async def test_list_categories(category_repo: CategoryRepository):
    """Test listing categories."""
    # Create some categories
    await category_repo.create_category("category1", "First category")
    await category_repo.create_category("category2", "Second category")
    await category_repo.create_category("category3", "Third category", is_active=False)
    
    # List active categories only
    active_categories = await category_repo.list_categories(active_only=True)
    active_names = [cat.name for cat in active_categories]
    assert "category1" in active_names
    assert "category2" in active_names
    assert "category3" not in active_names
    
    # List all categories
    all_categories = await category_repo.list_categories(active_only=False)
    all_names = [cat.name for cat in all_categories]
    assert "category1" in all_names
    assert "category2" in all_names
    assert "category3" in all_names


@pytest.mark.asyncio
async def test_product_without_categories(product_service: ProductService):
    """Test creating a product without categories."""
    product_data = ProductCreate(
        name="Product Without Categories",
        description="A product with no categories",
        price=Decimal("15.99"),
        categories=[]
    )

    current_user = {"id": 1, "email": "<EMAIL>"}
    response = await product_service.create_product(current_user, product_data)
    assert response.name == product_data.name
    assert len(response.categories) == 0


@pytest.mark.asyncio
async def test_clear_product_categories(product_service: ProductService):
    """Test clearing all categories from a product."""
    product_data = ProductCreate(
        name="Product to Clear Categories",
        description="A product to test category clearing",
        price=Decimal("25.99"),
        categories=["meat", "beef"]
    )

    current_user = {"id": 1, "email": "<EMAIL>"}
    product = await product_service.create_product(current_user, product_data)
    assert len(product.categories) == 2

    update_data = {"categories": []}
    updated_product = await product_service.update_product(product.id, update_data)

    assert len(updated_product.categories) == 0
