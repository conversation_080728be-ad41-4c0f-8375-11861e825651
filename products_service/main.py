"""
Products Service Main Application.

This module sets up the FastAPI application following the fehdan-inventory
service patterns including lifespan management, middleware configuration,
and event consumer initialization.
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy import text

from app.api.v1.endpoints.products import router as products_router
from app.core.config import settings
from app.core.database import engine, Base
from app.core.logging import LoggingService
from app.events.consumer import event_consumer


logger = LoggingService()


@asynccontextmanager
async def lifespan(_: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager following fehdan-inventory patterns.

    Handles startup and shutdown events including database initialization
    and event consumer management.
    """
    # Startup
    logger.log("Starting Products Service", level="info", app_name="products")

    try:
        # Create database tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.log("Database tables created", level="info", app_name="products")

        # Start event consumer
        consumer_task = asyncio.create_task(event_consumer.start_consuming())
        logger.log("Event consumer started", level="info", app_name="products")

        yield

    except Exception as e:
        logger.log(
            "Error during startup",
            level="error",
            exception=e,
            app_name="products"
        )
        raise
    finally:
        # Shutdown
        logger.log("Shutting down Products Service", level="info", app_name="products")

        # Stop event consumer
        if 'consumer_task' in locals():
            consumer_task.cancel()
            try:
                await consumer_task
            except asyncio.CancelledError:
                pass

        await event_consumer.stop_consuming()
        logger.log("Event consumer stopped", level="info", app_name="products")


# Create FastAPI application with lifespan management
app = FastAPI(
    title="Products Service",
    description="Product management service for Fehdan Meat Processing",
    version="1.0.0",
    lifespan=lifespan,
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(
    products_router,
    prefix="/api/v1/products",
    tags=["products"]
)


@app.get("/")
async def health_check():
    """
    Health check endpoint that verifies service and database connectivity.

    Returns:
        dict: Health status information including database connectivity
    """
    timestamp = datetime.now(timezone.utc).isoformat()

    try:
        # Test database connection
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

        return {
            "status": "healthy",
            "service": "products-service",
            "version": "1.0.0",
            "database": "connected",
            "timestamp": timestamp,
        }
    except Exception as e:
        logger.log(
            "Health check failed",
            level="error",
            exception=e,
            app_name="products"
        )
        return {
            "status": "unhealthy",
            "service": "products-service",
            "version": "1.0.0",
            "database": "disconnected",
            "error": str(e),
            "timestamp": timestamp,
        }


@app.get("/health")
async def health():
    """Health check endpoint for Docker and monitoring."""
    return await health_check()


@app.get("/api/v1/health")
async def api_health_check():
    """API-specific health check endpoint."""
    return await health_check()


# Future products routes can be added here
# app.include_router(products_router, prefix="/products", tags=["products"])
