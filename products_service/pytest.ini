[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    kafka: Tests that require Kafka
    database: Tests that require database
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
env =
    TESTING = true
    DATABASE_URL = sqlite+aiosqlite:///./test_products.db
