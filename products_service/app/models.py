"""
Database models for Products Service.

This module defines SQLAlchemy models following the fehdan-inventory
service patterns.
"""

from sqlalchemy import (
    Column,
    Integer,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Text,
    DECIMAL,
    JSON,
    Enum,
    Table,
)
from sqlalchemy.orm import relationship
from app.core.database import Base
import datetime
import enum


# Association table for many-to-many relationship between products and categories
product_categories = Table(
    'product_categories',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id'), primary_key=True),
    Column('category_id', Integer, ForeignKey('categories.id'), primary_key=True)
)


class Category(Base):
    """Category model for product categorization."""
    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text)
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
    )

    # Relationships
    products = relationship("Product", secondary=product_categories, back_populates="categories")


class AnimalType(str, enum.Enum):
    """Enumeration for animal types."""
    CHICKEN = "chicken"
    BEEF = "beef"
    PORK = "pork"
    LAMB = "lamb"
    TURKEY = "turkey"
    DUCK = "duck"
    FISH = "fish"
    SEAFOOD = "seafood"


class CutType(str, enum.Enum):
    """Enumeration for meat cut types."""
    WHOLE = "whole"
    BREAST = "breast"
    THIGH = "thigh"
    WING = "wing"
    DRUMSTICK = "drumstick"
    GROUND = "ground"
    STEAK = "steak"
    ROAST = "roast"
    CHOP = "chop"
    RIBS = "ribs"
    FILLET = "fillet"
    OTHER = "other"


class ProcessingLevel(str, enum.Enum):
    """Enumeration for processing levels."""
    RAW = "raw"
    MARINATED = "marinated"
    SEASONED = "seasoned"
    COOKED = "cooked"
    SMOKED = "smoked"
    CURED = "cured"
    FROZEN = "frozen"


class Product(Base):
    """Product model for meat products."""
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    price = Column(DECIMAL(10, 2), nullable=False)
    currency = Column(String(3), default="USD")
    in_stock = Column(Boolean, default=True)
    stock_quantity = Column(Integer, default=0)
    unity_of_measure = Column(String(20), default="lb")  # lb, kg, piece, pack
    tags = Column(JSON)  # Store as JSON array
    images = Column(JSON)  # Store as JSON array of image URLs

    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
    )

    # Relationships
    categories = relationship("Category", secondary=product_categories, back_populates="products")
    meat_details = relationship("MeatDetails", back_populates="product", uselist=False)
    nutritional_info = relationship("NutritionalInfo", back_populates="product", uselist=False)
    packaging = relationship("Packaging", back_populates="product", uselist=False)


class MeatDetails(Base):
    """Meat-specific details for products."""
    __tablename__ = "meat_details"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, unique=True)
    
    # Meat-specific attributes
    animal_type = Column(Enum(AnimalType), nullable=False)
    cut_type = Column(Enum(CutType), nullable=False)
    processing_level = Column(Enum(ProcessingLevel), default=ProcessingLevel.RAW)
    
    # Quality and sourcing
    grade = Column(String(20))  # USDA grade, etc.
    source_farm = Column(String(255))
    organic = Column(Boolean, default=False)
    grass_fed = Column(Boolean, default=False)
    free_range = Column(Boolean, default=False)
    
    # Physical attributes
    weight_range_min = Column(DECIMAL(8, 2))  # Minimum weight in specified unit
    weight_range_max = Column(DECIMAL(8, 2))  # Maximum weight in specified unit
    thickness = Column(DECIMAL(5, 2))  # Thickness in inches/cm
    
    # Storage and handling
    storage_temperature_min = Column(DECIMAL(5, 2))  # Min temp in Fahrenheit
    storage_temperature_max = Column(DECIMAL(5, 2))  # Max temp in Fahrenheit
    shelf_life_days = Column(Integer)  # Shelf life in days
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
    )

    # Relationships
    product = relationship("Product", back_populates="meat_details")


class NutritionalInfo(Base):
    """Nutritional information for products."""
    __tablename__ = "nutritional_info"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, unique=True)
    
    # Per serving information
    serving_size = Column(String(50))  # e.g., "4 oz", "100g"
    calories_per_serving = Column(Integer)
    
    # Macronutrients (in grams)
    protein = Column(DECIMAL(6, 2))
    total_fat = Column(DECIMAL(6, 2))
    saturated_fat = Column(DECIMAL(6, 2))
    trans_fat = Column(DECIMAL(6, 2))
    cholesterol = Column(DECIMAL(6, 2))  # in mg
    sodium = Column(DECIMAL(8, 2))  # in mg
    total_carbohydrates = Column(DECIMAL(6, 2))
    dietary_fiber = Column(DECIMAL(6, 2))
    sugars = Column(DECIMAL(6, 2))
    
    # Additional nutrients
    iron = Column(DECIMAL(6, 2))  # in mg
    calcium = Column(DECIMAL(6, 2))  # in mg
    vitamin_a = Column(DECIMAL(6, 2))  # in IU
    vitamin_c = Column(DECIMAL(6, 2))  # in mg
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
    )

    # Relationships
    product = relationship("Product", back_populates="nutritional_info")


class Packaging(Base):
    """Packaging information for products."""
    __tablename__ = "packaging"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, unique=True)
    
    # Packaging details
    package_type = Column(String(50))  # vacuum sealed, tray pack, bulk, etc.
    package_material = Column(String(50))  # plastic, paper, biodegradable, etc.
    package_size = Column(String(50))  # dimensions or description
    package_weight = Column(DECIMAL(8, 2))  # Package weight in specified unit
    
    # Labeling
    label_info = Column(JSON)  # Store label information as JSON
    barcode = Column(String(50), unique=True)
    lot_tracking = Column(Boolean, default=True)
    
    # Environmental
    recyclable = Column(Boolean, default=False)
    biodegradable = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
    )

    # Relationships
    product = relationship("Product", back_populates="packaging")
