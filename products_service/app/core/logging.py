"""
Logging configuration and service for Products Service.

This module provides structured logging following the fehdan-inventory
service patterns.
"""

import logging
import traceback
import os
from datetime import datetime


def setup_logging():
    """Setup logging configuration for the application."""
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
        ],
    )

    # Set specific log levels for different modules
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


class LoggingService:
    """
    Centralized logging service for the Products Service.
    
    Provides structured logging with different levels and exception handling.
    """
    
    def __init__(self):
        self.environment = os.getenv("ENVIRONMENT", "dev")

    def log(self, message, level="info", exception=None, app_name="products"):
        """
        Logs a message with the specified level and optional exception details.

        Args:
            message (str): The log message to record.
            level (str, optional): The severity level of the log.
                Supported values are "info", "warning", "error", and "critical". Defaults to "info".
            exception (Exception, optional): An exception object to include in the log, if any. Defaults to None.
            app_name (str, optional): The name of the application or module generating the log. Defaults to "products".

        Raises:
            ValueError: If an unsupported log level is provided.
        """

        if self.environment in ["dev", "test"] and exception:
            self.print_log(message, level, exception)

        if level == "info":
            self._log_info(message, app_name)
        elif level == "warning":
            self._log_warning(message, app_name)
        elif level == "error":
            self._log_error(message, exception, app_name)
        elif level == "critical":
            self._log_critical(message, exception, app_name)
        else:
            raise ValueError(f"Unsupported log level: {level}")

    def _log_info(self, message, app_name):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] INFO [{app_name}]: {message}")

    def _log_warning(self, message, app_name):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] WARNING [{app_name}]: {message}")

    def _log_error(self, message, exception=None, app_name="app"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        error_msg = f"[{timestamp}] ERROR [{app_name}]: {message}"
        if exception:
            error_msg += f" - Exception: {str(exception)}"
        print(error_msg)

    def _log_critical(self, message, exception=None, app_name="app"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        critical_msg = f"[{timestamp}] CRITICAL [{app_name}]: {message}"
        if exception:
            critical_msg += f" - Exception: {str(exception)}"
        print(critical_msg)

    def print_log(self, message, level="info", exception=None):
        """Print detailed traceback for development/testing environments."""
        if exception:
            print(f"\n--- TRACEBACK for {level.upper()} ---")
            print(f"Message: {message}")
            print(f"Exception: {str(exception)}")
            print("Traceback:")
            traceback.print_exc()
            print("--- END TRACEBACK ---\n")


# Usage example:
"""
logger = LoggingService()
logger.log("This is an info message", level="info", app_name="products")
logger.log("This is a warning message", level="warning", app_name="products")

try:
    1 / 0
except Exception as e:
    logger.log("An error occurred", level="error", exception=e, app_name="products")
    logger.log("A critical error occurred", level="critical", exception=e)
"""
