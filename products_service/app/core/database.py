"""
Database configuration and session management for Products Service.

This module provides async SQLAlchemy setup following the fehdan-inventory
service patterns.
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, DeclarativeBase
from contextlib import asynccontextmanager
from app.core.config import settings


# Create async engine with properly formatted URL
engine = create_async_engine(settings.async_database_url, echo=True)


# Create async session factory
async_session = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


# Base class for declarative models
class Base(DeclarativeBase):
    pass


# Function to create tables
async def create_tables():
    """Create all database tables."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


# Dependency to get DB session
async def get_db():
    """FastAPI dependency to get database session."""
    async with async_session() as session:
        yield session
