"""
Event consumer for Products Service.

This module provides event consumption capabilities following the
fehdan-inventory service patterns.
"""

import json
import asyncio
import logging
from typing import Dict, Any, Callable
from confluent_kafka import Consumer, KafkaError
from app.events.kafka_config import create_consumer, TOPICS
from app.events.schemas import (
    InventoryEventType,
    OrderEventType,
    InventoryItemCreatedEvent,
    OrderCreatedEvent,
    ALL_EVENT_MAPPINGS,
)
from app.core.logging import LoggingService

logger = logging.getLogger(__name__)


class EventConsumer:
    """
    Event consumer for Products Service.
    
    Handles consuming events from Kafka topics and processing them.
    """
    
    def __init__(self):
        self.consumer = None
        self.logger = LoggingService()
        self.event_handlers: Dict[str, Callable] = {}
        self.running = False
        
        # Register event handlers
        self._register_handlers()
    
    def _register_handlers(self):
        """Register event handlers for different event types."""
        self.event_handlers = {
            InventoryEventType.INVENTORY_ITEM_CREATED: self.handle_inventory_item_created,
            OrderEventType.ORDER_CREATED: self.handle_order_created,
        }
    
    async def handle_inventory_item_created(self, event_data: dict):
        """Handle INVENTORY_ITEM_CREATED event."""
        try:
            event = InventoryItemCreatedEvent(**event_data)
            self.logger.log(
                f"Processing inventory item created event: {event.event_id}",
                level="info",
                app_name="products"
            )
            
            # Business logic for handling inventory item creation
            inventory_data = event.data
            product_id = inventory_data.get("product_id")
            item_id = inventory_data.get("item_id")
            
            self.logger.log(
                f"Inventory item {item_id} created for product {product_id}",
                level="info",
                app_name="products"
            )
            
            # TODO: Implement any business logic needed when inventory items are created
            
        except Exception as e:
            self.logger.log(
                f"Error handling INVENTORY_ITEM_CREATED event: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
    
    async def handle_order_created(self, event_data: dict):
        """Handle ORDER_CREATED event."""
        try:
            event = OrderCreatedEvent(**event_data)
            self.logger.log(
                f"Processing order created event: {event.event_id}",
                level="info",
                app_name="products"
            )
            
            # Business logic for handling order creation
            order_data = event.data
            order_id = order_data.get("order_id")
            items = order_data.get("items", [])
            
            for item in items:
                product_id = item.get("product_id")
                quantity = item.get("quantity")
                
                self.logger.log(
                    f"Order {order_id} includes {quantity} units of product {product_id}",
                    level="info",
                    app_name="products"
                )
            
            # TODO: Implement any business logic needed when orders are created
            
        except Exception as e:
            self.logger.log(
                f"Error handling ORDER_CREATED event: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
    
    async def start_consuming(self, topics: list = None):
        """Start consuming events from specified topics."""
        try:
            if topics is None:
                topics = [TOPICS['inventory'], TOPICS['orders']]

            # Try to create consumer with error handling
            try:
                self.consumer = create_consumer(
                    group_id="products-service-consumer",
                    topics=topics
                )
                self.logger.log(
                    f"Kafka consumer created successfully for topics: {topics}",
                    level="info",
                    app_name="products"
                )

                # Test connection by trying to get metadata
                metadata = self.consumer.list_topics(timeout=5)
                if not metadata:
                    raise Exception("Unable to retrieve Kafka metadata - Kafka may not be available")

            except Exception as kafka_error:
                self.logger.log(
                    f"Failed to create Kafka consumer: {kafka_error}. Service will continue without event consumption.",
                    level="warning",
                    app_name="products"
                )
                return  # Exit gracefully if Kafka is not available

            self.running = True
            self.logger.log(
                f"Started consuming from topics: {topics}",
                level="info",
                app_name="products"
            )

            consecutive_errors = 0
            max_consecutive_errors = 10

            while self.running:
                try:
                    # Use asyncio.sleep to make this non-blocking
                    await asyncio.sleep(0.1)

                    msg = self.consumer.poll(timeout=0.1)  # Shorter timeout for responsiveness

                    if msg is None:
                        consecutive_errors = 0  # Reset error count on successful poll
                        continue

                    if msg.error():
                        if msg.error().code() == KafkaError._PARTITION_EOF:
                            consecutive_errors = 0  # Reset error count
                            continue
                        else:
                            consecutive_errors += 1
                            self.logger.log(
                                f"Consumer error: {msg.error()}",
                                level="error",
                                app_name="products"
                            )

                            # If too many consecutive errors, stop consuming
                            if consecutive_errors >= max_consecutive_errors:
                                self.logger.log(
                                    f"Too many consecutive errors ({consecutive_errors}). Stopping consumer.",
                                    level="error",
                                    app_name="products"
                                )
                                break

                            # Wait before retrying
                            await asyncio.sleep(1)
                            continue

                    # Process the message
                    consecutive_errors = 0  # Reset error count on successful message
                    await self._process_message_async(msg)

                except Exception as poll_error:
                    consecutive_errors += 1
                    self.logger.log(
                        f"Error during polling: {poll_error}",
                        level="error",
                        app_name="products"
                    )

                    if consecutive_errors >= max_consecutive_errors:
                        self.logger.log(
                            f"Too many consecutive polling errors ({consecutive_errors}). Stopping consumer.",
                            level="error",
                            app_name="products"
                        )
                        break

                    # Wait before retrying
                    await asyncio.sleep(1)

        except Exception as e:
            self.logger.log(
                f"Error in event consumer: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
        finally:
            self.running = False
            if self.consumer:
                try:
                    self.consumer.close()
                except Exception as close_error:
                    self.logger.log(
                        f"Error closing consumer: {close_error}",
                        level="warning",
                        app_name="products"
                    )

    def stop_consuming(self):
        """Stop the event consumer."""
        self.running = False
        self.logger.log(
            "Event consumer stopped",
            level="info",
            app_name="products"
        )
    
    async def _process_message_async(self, msg):
        """Process a received Kafka message asynchronously."""
        try:
            # Decode message
            message_value = msg.value().decode('utf-8')
            event_data = json.loads(message_value)

            event_type = event_data.get('event_type')
            if not event_type:
                self.logger.log(
                    "Received message without event_type",
                    level="warning",
                    app_name="products"
                )
                return

            self.logger.log(
                f"Received event: {event_type}",
                level="info",
                app_name="products"
            )

            # Find and execute handler
            handler = self.event_handlers.get(event_type)
            if handler:
                # All handlers are async now
                if asyncio.iscoroutinefunction(handler):
                    await handler(event_data)
                else:
                    # Run synchronous handler in thread pool to avoid blocking
                    await asyncio.get_event_loop().run_in_executor(None, handler, event_data)
            else:
                self.logger.log(
                    f"No handler registered for event type: {event_type}",
                    level="debug",
                    app_name="products"
                )

        except Exception as e:
            self.logger.log(
                f"Error processing message: {e}",
                level="error",
                exception=e,
                app_name="products"
            )

    def _process_message(self, msg):
        """Process a received Kafka message (legacy sync method)."""
        try:
            # Decode message
            message_value = msg.value().decode('utf-8')
            event_data = json.loads(message_value)

            event_type = event_data.get('event_type')
            if not event_type:
                self.logger.log(
                    "Received message without event_type",
                    level="warning",
                    app_name="products"
                )
                return

            self.logger.log(
                f"Received event: {event_type}",
                level="info",
                app_name="products"
            )

            # Find and execute handler
            handler = self.event_handlers.get(event_type)
            if handler:
                # Check if handler is async and run appropriately
                if asyncio.iscoroutinefunction(handler):
                    # Run async handler in new event loop if none exists
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If loop is running, create task
                            asyncio.create_task(handler(event_data))
                        else:
                            # If no loop is running, run directly
                            loop.run_until_complete(handler(event_data))
                    except RuntimeError:
                        # No event loop exists, create and run
                        asyncio.run(handler(event_data))
                else:
                    # Synchronous handler
                    handler(event_data)
            else:
                self.logger.log(
                    f"No handler registered for event type: {event_type}",
                    level="debug",
                    app_name="products"
                )

        except Exception as e:
            self.logger.log(
                f"Error processing message: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
    
    async def stop_consuming(self):
        """Stop consuming events."""
        self.running = False
        self.logger.log(
            "Stopping event consumer",
            level="info",
            app_name="products"
        )
        # Give a moment for the consumer loop to finish
        await asyncio.sleep(0.2)


# Global event consumer instance
event_consumer = EventConsumer()
