"""
Event publisher for Products Service.

This module provides event publishing capabilities following the
fehdan-inventory service patterns.
"""

import json
import logging
from typing import Union, Optional, Dict, Any
from confluent_kafka import Producer
from app.events.kafka_config import create_producer, TOPICS
from app.events.schemas import (
    BaseEvent,
    ProductCreatedEvent,
    ProductUpdatedEvent,
    ProductDeletedEvent,
    CategoryCreatedEvent,
)
from app.core.logging import LoggingService

logger = logging.getLogger(__name__)


class EventPublisher:
    """
    Event publisher for Products Service.
    
    Handles publishing events to Kafka topics following structured patterns.
    """
    
    def __init__(self):
        self.producer = None
        self.products_topic = TOPICS['products']
        self.logger = LoggingService()
    
    def _get_producer(self) -> Producer:
        """Get or create Kafka producer instance."""
        if self.producer is None:
            self.producer = create_producer()
        return self.producer
    
    def _delivery_callback(self, err, msg):
        """Callback for message delivery confirmation."""
        if err is not None:
            self.logger.log(
                f"Message delivery failed: {err}",
                level="error",
                app_name="products"
            )
        else:
            self.logger.log(
                f"Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}",
                level="info",
                app_name="products"
            )
    
    async def publish_event(
        self, event: Union[BaseEvent, dict], topic: str = None, key: str = None
    ):
        """Publish an event to the specified topic or default products topic."""
        try:
            topic = topic or self.products_topic
            producer = self._get_producer()
            
            # Convert Pydantic model to dict if needed
            if hasattr(event, "model_dump"):
                event_data = event.model_dump()
            elif isinstance(event, dict):
                event_data = event
            else:
                event_data = event.__dict__
            
            # Serialize event data to JSON
            message_value = json.dumps(event_data)
            
            # Use event_id as key if no key provided
            message_key = key or event_data.get("event_id", "default")
            
            # Produce message to Kafka
            producer.produce(
                topic=topic,
                key=message_key,
                value=message_value,
                callback=self._delivery_callback,
            )
            
            # Flush to ensure message is sent
            producer.flush(timeout=10)
            
            self.logger.log(
                f"Published event {event_data.get('event_type')} to topic {topic}",
                level="info",
                app_name="products"
            )
            
        except Exception as e:
            self.logger.log(
                f"Failed to publish event: {str(e)}",
                level="error",
                exception=e,
                app_name="products"
            )
            raise
    
    async def publish_product_created(
        self,
        product_id: int,
        product_name: str,
        price: float,
        currency: str = "USD",
        stock_quantity: int = 0,
        created_by: str = "system",
        categories: Optional[list] = None,
    ):
        """Publish PRODUCT_CREATED event."""
        event = ProductCreatedEvent.create(
            product_id=product_id,
            product_name=product_name,
            price=price,
            currency=currency,
            stock_quantity=stock_quantity,
            created_by=created_by,
            categories=categories,
        )
        await self.publish_event(event)
    
    async def publish_product_updated(
        self,
        product_id: int,
        product_name: str,
        updated_fields: dict,
        updated_by: str = "system",
    ):
        """Publish PRODUCT_UPDATED event."""
        event = ProductUpdatedEvent.create(
            product_id=product_id,
            product_name=product_name,
            updated_fields=updated_fields,
            updated_by=updated_by,
        )
        await self.publish_event(event)
    
    async def publish_product_deleted(
        self,
        product_id: int,
        product_name: str,
        deleted_by: str = "system",
    ):
        """Publish PRODUCT_DELETED event."""
        event = ProductDeletedEvent.create(
            product_id=product_id,
            product_name=product_name,
            deleted_by=deleted_by,
        )
        await self.publish_event(event)
    
    async def publish_category_created(
        self,
        category_id: int,
        category_name: str,
        description: Optional[str] = None,
        created_by: str = "system",
    ):
        """Publish CATEGORY_CREATED event."""
        event = CategoryCreatedEvent.create(
            category_id=category_id,
            category_name=category_name,
            description=description,
            created_by=created_by,
        )
        await self.publish_event(event)
    
    def close(self):
        """Close the producer and clean up resources."""
        if self.producer:
            self.producer.flush()
            self.producer = None


# Global event publisher instance
event_publisher = EventPublisher()
