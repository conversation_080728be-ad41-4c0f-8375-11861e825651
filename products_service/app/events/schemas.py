"""
Event schemas for Products Service.

This module defines event types and schemas following the fehdan-inventory
service patterns for event-driven architecture.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class ProductEventType(str, Enum):
    """Product channel event types (published)."""
    
    PRODUCT_CREATED = "PRODUCT_CREATED"
    PRODUCT_UPDATED = "PRODUCT_UPDATED"
    PRODUCT_DELETED = "PRODUCT_DELETED"
    CATEGORY_CREATED = "CATEGORY_CREATED"
    CATEGORY_UPDATED = "CATEGORY_UPDATED"
    CATEGORY_DELETED = "CATEGORY_DELETED"


class InventoryEventType(str, Enum):
    """Inventory channel event types (consumed)."""
    
    INVENTORY_ITEM_CREATED = "INVENTORY_ITEM_CREATED"
    INVENTORY_ITEM_UPDATED = "INVENTORY_ITEM_UPDATED"
    INVENTORY_ITEM_DELETED = "INVENTORY_ITEM_DELETED"
    STOCK_LEVEL_CHANGED = "STOCK_LEVEL_CHANGED"
    LOW_STOCK_ALERT = "LOW_STOCK_ALERT"


class OrderEventType(str, Enum):
    """Order channel event types (consumed)."""
    
    ORDER_CREATED = "ORDER_CREATED"
    ORDER_CANCELLED = "ORDER_CANCELLED"
    ORDER_FULFILLED = "ORDER_FULFILLED"


class BaseEvent(BaseModel):
    """Base event schema."""
    
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="products_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]


class ProductCreatedEvent(BaseModel):
    """Event published when a product is created."""
    
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=ProductEventType.PRODUCT_CREATED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="products_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]
    
    @classmethod
    def create(
        cls,
        product_id: int,
        product_name: str,
        price: float,
        currency: str = "USD",
        stock_quantity: int = 0,
        created_by: str = "system",
        categories: Optional[list] = None,
    ):
        return cls(
            data={
                "product_id": product_id,
                "name": product_name,
                "price": price,
                "currency": currency,
                "stock_quantity": stock_quantity,
                "created_by": created_by,
                "categories": categories or [],
            }
        )


class ProductUpdatedEvent(BaseModel):
    """Event published when a product is updated."""
    
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=ProductEventType.PRODUCT_UPDATED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="products_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]
    
    @classmethod
    def create(
        cls,
        product_id: int,
        product_name: str,
        updated_fields: dict,
        updated_by: str = "system",
    ):
        return cls(
            data={
                "product_id": product_id,
                "name": product_name,
                "updated_fields": updated_fields,
                "updated_by": updated_by,
            }
        )


class ProductDeletedEvent(BaseModel):
    """Event published when a product is deleted."""
    
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=ProductEventType.PRODUCT_DELETED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="products_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]
    
    @classmethod
    def create(
        cls,
        product_id: int,
        product_name: str,
        deleted_by: str = "system",
    ):
        return cls(
            data={
                "product_id": product_id,
                "name": product_name,
                "deleted_by": deleted_by,
            }
        )


class CategoryCreatedEvent(BaseModel):
    """Event published when a category is created."""
    
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=ProductEventType.CATEGORY_CREATED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="products_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]
    
    @classmethod
    def create(
        cls,
        category_id: int,
        category_name: str,
        description: Optional[str] = None,
        created_by: str = "system",
    ):
        return cls(
            data={
                "category_id": category_id,
                "name": category_name,
                "description": description,
                "created_by": created_by,
            }
        )


# Event consumed from other services
class InventoryItemCreatedEvent(BaseModel):
    """Event consumed when inventory item is created."""
    
    event_id: str
    event_type: str
    timestamp: str
    service: str
    version: str
    data: Dict[str, Any]


class OrderCreatedEvent(BaseModel):
    """Event consumed when order is created."""
    
    event_id: str
    event_type: str
    timestamp: str
    service: str
    version: str
    data: Dict[str, Any]


# Event type mapping for deserialization
PRODUCT_EVENT_MAPPING = {
    ProductEventType.PRODUCT_CREATED: ProductCreatedEvent,
    ProductEventType.PRODUCT_UPDATED: ProductUpdatedEvent,
    ProductEventType.PRODUCT_DELETED: ProductDeletedEvent,
    ProductEventType.CATEGORY_CREATED: CategoryCreatedEvent,
}

INVENTORY_EVENT_MAPPING = {
    InventoryEventType.INVENTORY_ITEM_CREATED: InventoryItemCreatedEvent,
}

ORDER_EVENT_MAPPING = {
    OrderEventType.ORDER_CREATED: OrderCreatedEvent,
}


# All event mappings combined
ALL_EVENT_MAPPINGS = {
    **PRODUCT_EVENT_MAPPING,
    **INVENTORY_EVENT_MAPPING,
    **ORDER_EVENT_MAPPING,
}
