"""
Product Repository for Products Service.

This module provides data access layer for products following the
fehdan-inventory service patterns.
"""

from typing import List, Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete, update, and_, or_
from sqlalchemy.orm import selectinload

from app.core.logging import LoggingService
from app.models import Product, Category
from app.schemas import ProductCreate, ProductUpdate, ProductResponse


class ProductRepository:
    """
    Repository for product data access operations.

    Provides CRUD operations for products following fehdan-inventory patterns
    with proper error handling and logging.
    """

    def __init__(self, db: AsyncSession):
        self.db = db
        self.logger = LoggingService()

    async def create_product(self, product_data: ProductCreate) -> ProductResponse:
        """Create a new product."""
        try:
            product_dict = product_data.model_dump()
            product_dict.pop('categories', None)

            db_product = Product(**product_dict)
            self.db.add(db_product)
            await self.db.commit()
            await self.db.refresh(db_product, ['categories'])

            return ProductResponse.model_validate(db_product)
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error creating product",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def get_product_by_id(self, product_id: int) -> Optional[ProductResponse]:
        """Retrieve a product by ID."""
        try:
            result = await self.db.execute(
                select(Product)
                .options(selectinload(Product.categories))
                .filter_by(id=product_id)
            )
            product = result.scalar_one_or_none()
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")
            return ProductResponse.model_validate(product)
        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                message="Error retrieving product",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def update_product(
        self, product_id: int, product_data: ProductUpdate
    ) -> Optional[ProductResponse]:
        """Update product details."""
        try:
            result = await self.db.execute(
                select(Product)
                .options(selectinload(Product.categories))
                .where(Product.id == product_id)
            )
            product = result.scalar_one_or_none()

            if not product:
                raise HTTPException(status_code=404, detail="Product not found")

            update_data = product_data.model_dump(exclude_unset=True, exclude_none=True)
            update_data.pop('categories', None)

            for key, value in update_data.items():
                setattr(product, key, value)

            await self.db.commit()
            await self.db.refresh(product, ['categories'])

            return ProductResponse.model_validate(product)
        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error updating product",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def delete_product(self, product_id: int) -> None:
        """Delete a product by ID."""
        try:
            result = await self.db.execute(select(Product).filter_by(id=product_id))
            product = result.scalar_one_or_none()
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")

            await self.db.execute(delete(Product).where(Product.id == product_id))
            await self.db.commit()
        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error deleting product",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error deleting product")

    async def get_product_by_name(self, name: str) -> Optional[ProductResponse]:
        """Retrieve a product by name."""
        try:
            result = await self.db.execute(
                select(Product)
                .options(selectinload(Product.categories))
                .filter_by(name=name)
            )
            product = result.scalar_one_or_none()
            if product:
                return ProductResponse.model_validate(product)
            return None
        except Exception as e:
            self.logger.log(
                message="Error retrieving product by name",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def list_products(
        self,
        category: Optional[str] = None,
        in_stock: Optional[bool] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Optional[List[ProductResponse]]:
        """List products with optional filtering and pagination."""
        try:
            query = select(Product).options(selectinload(Product.categories))

            # Apply filters
            if category:
                query = query.join(Product.categories).where(Category.name == category)

            if in_stock is not None:
                query = query.where(Product.in_stock == in_stock)

            if search:
                search_term = f"%{search}%"
                query = query.where(
                    or_(
                        Product.name.ilike(search_term),
                        Product.description.ilike(search_term)
                    )
                )

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await self.db.execute(query)
            products = result.scalars().all()

            return [ProductResponse.model_validate(product) for product in products]

        except Exception as e:
            self.logger.log(
                message="Error listing products",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def advanced_search(
        self,
        name: Optional[str] = None,
        description: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        categories: Optional[List[str]] = None,
        tags: Optional[List[str]] = None,
        in_stock: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Optional[List[ProductResponse]]:
        """Advanced product search with multiple criteria."""
        try:
            query = select(Product).options(selectinload(Product.categories))
            conditions = []

            if name:
                conditions.append(Product.name.ilike(f"%{name}%"))

            if description:
                conditions.append(Product.description.ilike(f"%{description}%"))

            if min_price is not None:
                conditions.append(Product.price >= min_price)

            if max_price is not None:
                conditions.append(Product.price <= max_price)

            if in_stock is not None:
                conditions.append(Product.in_stock == in_stock)

            if categories:
                query = query.join(Product.categories).where(Category.name.in_(categories))

            if tags:
                # Assuming tags are stored as JSON array
                for tag in tags:
                    conditions.append(Product.tags.contains([tag]))

            if conditions:
                query = query.where(and_(*conditions))

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await self.db.execute(query)
            products = result.scalars().all()

            return [ProductResponse.model_validate(product) for product in products]

        except Exception as e:
            self.logger.log(
                message="Error in advanced search",
                level="error",
                exception=e,
                app_name="products",
            )
            return None
