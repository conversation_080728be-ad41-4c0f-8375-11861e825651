"""
Pydantic schemas for Products Service.

This module defines request/response schemas following the fehdan-inventory
service patterns.
"""

import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field
from decimal import Decimal


# Authentication schemas
class CurrentUserResponse(BaseModel):
    """Schema for current user response from authentication."""
    id: int
    email: str
    is_active: bool
    is_email_verified: bool
    role: str
    full_name: Optional[str] = None


# Category schemas
class CategoryBase(BaseModel):
    """Base category schema."""
    name: str
    description: Optional[str] = None
    is_active: Optional[bool] = True


class CategoryCreate(CategoryBase):
    """Schema for creating a category."""
    pass


class CategoryUpdate(BaseModel):
    """Schema for updating a category."""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class CategoryResponse(CategoryBase):
    """Schema for category response."""
    id: int
    created_at: Optional[datetime.datetime] = None
    updated_at: Optional[datetime.datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Product schemas
class ProductBase(BaseModel):
    """Base product schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    price: Decimal = Field(..., gt=0, decimal_places=2)
    currency: Optional[str] = Field("USD", max_length=3)
    in_stock: Optional[bool] = True
    stock_quantity: Optional[int] = Field(0, ge=0)
    unity_of_measure: Optional[str] = Field("lb", max_length=20)
    categories: Optional[List[str]] = Field(default_factory=list)
    tags: Optional[List[str]] = Field(default_factory=list)
    images: Optional[List[str]] = Field(default_factory=list)


class ProductCreate(ProductBase):
    """Schema for creating a product."""
    pass


class ProductUpdate(BaseModel):
    """Schema for updating a product."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    currency: Optional[str] = Field(None, max_length=3)
    in_stock: Optional[bool] = None
    stock_quantity: Optional[int] = Field(None, ge=0)
    unity_of_measure: Optional[str] = Field(None, max_length=20)
    categories: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    images: Optional[List[str]] = None


class ProductResponse(ProductBase):
    """Schema for product response."""
    id: int
    categories: List[CategoryResponse] = Field(default_factory=list)
    created_at: Optional[datetime.datetime] = None
    updated_at: Optional[datetime.datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Search and filter schemas
class ProductSearchFilters(BaseModel):
    """Schema for product search filters."""
    name: Optional[str] = None
    description: Optional[str] = None
    min_price: Optional[Decimal] = Field(None, ge=0)
    max_price: Optional[Decimal] = Field(None, ge=0)
    categories: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    in_stock: Optional[bool] = None
    unity_of_measure: Optional[str] = None


class ProductAvailabilityResponse(BaseModel):
    """Schema for product availability response."""
    product_id: int
    available: bool
    stock_quantity: int
    requested_quantity: int
    message: str


# Pagination schemas
class PaginationParams(BaseModel):
    """Schema for pagination parameters."""
    limit: int = Field(100, ge=1, le=1000)
    offset: int = Field(0, ge=0)


class PaginatedResponse(BaseModel):
    """Schema for paginated response."""
    items: List[Any]
    total: int
    limit: int
    offset: int
    has_next: bool
    has_previous: bool


# Error schemas
class ErrorResponse(BaseModel):
    """Schema for error responses."""
    detail: str
    error_code: Optional[str] = None
    timestamp: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)


class ValidationErrorResponse(BaseModel):
    """Schema for validation error responses."""
    detail: str
    errors: List[Dict[str, Any]]
    timestamp: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
