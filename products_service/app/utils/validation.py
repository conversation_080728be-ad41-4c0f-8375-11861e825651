"""
Validation utilities for Products Service.

This module provides business rule validation following the
fehdan-inventory service patterns.
"""

from typing import Optional, List
from decimal import Decimal
from fastapi import HTTPEx<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import LoggingService
from app.repositories.product import ProductRepository
from app.repositories.category import CategoryRepository
from app.schemas import ProductCreate, ProductUpdate


class ValidationUtils:
    """
    Validation utilities for product operations.
    
    Provides business rule validation for product creation, updates,
    and category management.
    """
    
    def __init__(self, db: AsyncSession = None):
        self.db = db
        if db:
            self.product_repo = ProductRepository(db)
            self.category_repo = CategoryRepository(db)
        self.logger = LoggingService()
    
    async def validate_product_creation(self, product_data: ProductCreate) -> None:
        """Validate business rules for product creation."""
        
        # Validate product name uniqueness
        await self.validate_product_name_unique(product_data.name)
        
        # Validate price
        self.validate_price(product_data.price)
        
        # Validate currency
        self.validate_currency(product_data.currency)
        
        # Validate stock quantity
        if product_data.stock_quantity is not None:
            self.validate_stock_quantity(product_data.stock_quantity)
        
        # Validate unit of measure
        if product_data.unity_of_measure:
            self.validate_unit_of_measure(product_data.unity_of_measure)
        
        # Validate categories if provided
        if product_data.categories:
            await self.validate_categories_exist(product_data.categories)
    
    async def validate_product_update(
        self, product_id: int, product_data: ProductUpdate
    ) -> None:
        """Validate business rules for product update."""
        
        # Check if product exists
        await self.validate_product_exists(product_id)
        
        # Validate name uniqueness if name is being updated
        if product_data.name is not None:
            await self.validate_product_name_unique(product_data.name, exclude_id=product_id)
        
        # Validate price if being updated
        if product_data.price is not None:
            self.validate_price(product_data.price)
        
        # Validate currency if being updated
        if product_data.currency is not None:
            self.validate_currency(product_data.currency)
        
        # Validate stock quantity if being updated
        if product_data.stock_quantity is not None:
            self.validate_stock_quantity(product_data.stock_quantity)
        
        # Validate unit of measure if being updated
        if product_data.unity_of_measure is not None:
            self.validate_unit_of_measure(product_data.unity_of_measure)
        
        # Validate categories if being updated
        if product_data.categories is not None:
            await self.validate_categories_exist(product_data.categories)
    
    async def validate_product_exists(self, product_id: int) -> bool:
        """Check if product exists."""
        try:
            if not self.product_repo:
                return False
            
            product = await self.product_repo.get_product_by_id(product_id)
            return product is not None
        except HTTPException as e:
            if e.status_code == 404:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Product with ID {product_id} not found",
                )
            raise
        except Exception as e:
            self.logger.log(
                f"Error validating product existence {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            return False
    
    async def validate_product_name_unique(
        self, name: str, exclude_id: Optional[int] = None
    ) -> None:
        """Validate that product name is unique."""
        try:
            if not self.product_repo:
                return
            
            existing_product = await self.product_repo.get_product_by_name(name)
            if existing_product and (exclude_id is None or existing_product.id != exclude_id):
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Product with name '{name}' already exists",
                )
        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                f"Error validating product name uniqueness: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
    
    def validate_price(self, price: Decimal) -> None:
        """Validate product price."""
        if price <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Product price must be greater than 0",
            )
        
        if price > Decimal('999999.99'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Product price cannot exceed $999,999.99",
            )
    
    def validate_currency(self, currency: str) -> None:
        """Validate currency code."""
        valid_currencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD']
        if currency not in valid_currencies:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid currency code. Supported currencies: {', '.join(valid_currencies)}",
            )
    
    def validate_stock_quantity(self, quantity: int) -> None:
        """Validate stock quantity."""
        if quantity < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Stock quantity cannot be negative",
            )
        
        if quantity > 1000000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Stock quantity cannot exceed 1,000,000",
            )
    
    def validate_unit_of_measure(self, unit: str) -> None:
        """Validate unit of measure."""
        valid_units = ['lb', 'kg', 'piece', 'pack', 'box', 'case', 'oz', 'g']
        if unit not in valid_units:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid unit of measure. Supported units: {', '.join(valid_units)}",
            )
    
    async def validate_categories_exist(self, category_names: List[str]) -> None:
        """Validate that all specified categories exist."""
        try:
            if not self.category_repo:
                return
            
            for category_name in category_names:
                category = await self.category_repo.get_category_by_name(category_name)
                if not category:
                    # Auto-create category if it doesn't exist
                    await self.category_repo.create_category(
                        name=category_name,
                        description=f"Auto-created category: {category_name}",
                        is_active=True
                    )
                    self.logger.log(
                        f"Auto-created category: {category_name}",
                        level="info",
                        app_name="products",
                    )
        except Exception as e:
            self.logger.log(
                f"Error validating categories: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error validating categories: {str(e)}",
            )
