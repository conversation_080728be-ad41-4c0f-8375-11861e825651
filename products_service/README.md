# Products Service

A standalone microservice for managing products in the Fehadan Meat Processing system.

## Features

- Product management (CRUD operations)
- Category management
- Product search and filtering
- Event-driven architecture with Kafka integration
- RESTful API with FastAPI
- SQLite/PostgreSQL database support
- JWT authentication integration

## Quick Start

### Prerequisites

- Python 3.13+
- Docker (optional)

### Local Development

1. **Clone and navigate to the service directory:**
   ```bash
   cd products_service
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Run the service:**
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8002 --reload
   ```

### Docker Deployment

1. **Build and run with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

2. **Or build and run manually:**
   ```bash
   docker build -t products-service .
   docker run -p 8002:8002 --env-file .env products-service
   ```

## API Documentation

Once running, visit:
- **Swagger UI:** http://localhost:8002/docs
- **ReDoc:** http://localhost:8002/redoc

## Configuration

The service can be configured through environment variables. See `.env.example` for all available options.

### Key Configuration Options

- `DATABASE_URL`: Database connection string
- `JWT_SECRET_KEY`: Secret key for JWT token validation
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka broker addresses (optional)
- `DEBUG`: Enable debug mode

## Architecture

The service follows a clean architecture pattern:

```
products_service/
├── app/
│   ├── api/           # API endpoints
│   ├── core/          # Core configuration and utilities
│   ├── events/        # Event handling (Kafka)
│   ├── models.py      # Database models
│   ├── repositories/  # Data access layer
│   ├── schemas.py     # Pydantic schemas
│   ├── services/      # Business logic
│   └── utils/         # Utility functions
├── tests/             # Test suite
├── main.py           # Application entry point
└── requirements.txt  # Python dependencies
```

## Testing

Run the test suite:

```bash
pytest
```

## Health Check

The service provides a health check endpoint:

```bash
curl http://localhost:8002/health
```

## Integration

This service can run independently or as part of the larger Fehadan Meat Processing system. It communicates with other services through:

- **REST APIs** for synchronous communication
- **Kafka events** for asynchronous communication (optional)

## Development

### Adding New Features

1. Add models in `app/models.py`
2. Create schemas in `app/schemas.py`
3. Implement repository methods in `app/repositories/`
4. Add business logic in `app/services/`
5. Create API endpoints in `app/api/`
6. Write tests in `tests/`

### Code Style

The project follows Python best practices:
- Type hints
- Docstrings
- Clean architecture principles
- Dependency injection

## License

This project is part of the Fehadan Meat Processing system.
