#!/usr/bin/env python3
"""
Test script to verify that the Products Service can run standalone.

This script tests the basic functionality of the service without external dependencies.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_standalone():
    """Test that the service can start and respond to basic requests."""
    print("🧪 Testing Products Service standalone functionality...")
    
    try:
        # Set testing environment
        os.environ["TESTING"] = "true"
        os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///./test_standalone.db"
        
        # Import after setting environment
        from app.core.config import settings
        from app.core.database import engine, Base
        from app.models import Product, Category
        from app.repositories.product import ProductRepository
        from app.services.product import ProductService
        from app.schemas import ProductCreate, CategoryCreate
        
        print("✅ All imports successful")
        
        # Test database connection
        print("🔍 Testing database connection...")
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ Database connection successful")
        
        # Test repository and service
        print("🔍 Testing repository and service...")
        from app.core.database import async_session

        # Create a database session for testing
        async with async_session() as session:
            product_repo = ProductRepository(session)
            product_service = ProductService(session)

            # Create a test category directly through repository
            from app.repositories.category import CategoryRepository
            category_repo = CategoryRepository(session)
            category = await category_repo.create_category(
                name="Test Category",
                description="A test category",
                is_active=True
            )
            print(f"✅ Created test category: {category.name}")

            # Create a test product
            product_data = ProductCreate(
                name="Test Product",
                description="A test product",
                price=10.99,
                currency="USD",
                in_stock=True,
                stock_quantity=100,
                unity_of_measure="kg",
                tags=["test", "meat"],
                images=["test.jpg"]
            )

            # Mock current user for the service call
            current_user = {
                "id": 1,
                "email": "<EMAIL>",
                "role": "admin"
            }

            product = await product_service.create_product(current_user, product_data)
            print(f"✅ Created test product: {product.name}")

            # Test retrieval
            retrieved_product = await product_service.get_product_by_id(product.id)
            assert retrieved_product is not None
            assert retrieved_product.name == "Test Product"
            print("✅ Product retrieval successful")

            # Test listing
            products = await product_service.list_products()
            assert len(products) >= 1
            print(f"✅ Product listing successful ({len(products)} products)")

            print("🎉 All standalone tests passed!")
            return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup test database
        test_db_path = Path("test_standalone.db")
        if test_db_path.exists():
            test_db_path.unlink()
            print("🧹 Cleaned up test database")

async def test_kafka_optional():
    """Test that Kafka is optional and service works without it."""
    print("🧪 Testing Kafka optional functionality...")
    
    try:
        # Import event consumer
        from app.events.consumer import EventConsumer
        
        # Try to start consumer (should fail gracefully)
        consumer = EventConsumer()
        await consumer.start_consuming()
        
        print("✅ Kafka consumer handles unavailable Kafka gracefully")
        return True
        
    except Exception as e:
        print(f"❌ Kafka test failed: {e}")
        return False

async def main():
    """Run all standalone tests."""
    print("🚀 Starting Products Service Standalone Tests")
    print("=" * 50)
    
    # Test basic functionality
    basic_test_passed = await test_standalone()
    
    # Test Kafka optional
    kafka_test_passed = await test_kafka_optional()
    
    print("=" * 50)
    if basic_test_passed and kafka_test_passed:
        print("🎉 All tests passed! Products Service is ready for standalone deployment.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
