#!/bin/bash

# Products Service Deployment Script
# This script handles the deployment of the Products Service

set -e  # Exit on any error

echo "🚀 Starting Products Service Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from template..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status "Created .env from .env.example"
        print_warning "Please edit .env file with your configuration before running again."
        exit 1
    else
        print_error ".env.example not found. Please create .env file manually."
        exit 1
    fi
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Build and deploy with Docker Compose
print_status "Building and starting Products Service..."

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose down

# Build and start services
print_status "Building and starting services..."
docker-compose up --build -d

# Wait for service to be healthy
print_status "Waiting for service to be healthy..."
sleep 10

# Check health
MAX_RETRIES=30
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -f http://localhost:8002/health > /dev/null 2>&1; then
        print_status "✅ Products Service is healthy and running!"
        break
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        print_status "Waiting for service... (attempt $RETRY_COUNT/$MAX_RETRIES)"
        sleep 2
    fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    print_error "❌ Service failed to become healthy within expected time"
    print_status "Checking logs..."
    docker-compose logs
    exit 1
fi

# Show running containers
print_status "Running containers:"
docker-compose ps

print_status "🎉 Products Service deployment completed successfully!"
print_status "Service is available at: http://localhost:8002"
print_status "API Documentation: http://localhost:8002/docs"
print_status "Health Check: http://localhost:8002/health"

echo ""
print_status "Useful commands:"
echo "  View logs: docker-compose logs -f"
echo "  Stop service: docker-compose down"
echo "  Restart service: docker-compose restart"
echo "  View status: docker-compose ps"
