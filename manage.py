#!/usr/bin/env python3
"""
Management script for <PERSON><PERSON><PERSON> Meat Processing Backend.

Provides CLI commands for administrative tasks like creating superusers.
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

import typer
from sqlalchemy.ext.asyncio import AsyncSession
from shared.database import get_db_session
from auth_service.repositories.user_repository import UserRepository
from auth_service.services.auth_service import AuthService
from auth_service.models import User, UserRole
from auth_service.schemas import UserCreate

app = typer.Typer(help="Fehadan Meat Processing Management Commands")


@app.command()
def create_superuser(
    email: str = typer.Option(None, "--email", help="Superuser email address"),
    password: str = typer.Option(None, "--password", help="Superuser password"),
    first_name: str = typer.Option(None, "--first-name", help="First name"),
    last_name: str = typer.Option(None, "--last-name", help="Last name"),
):
    """Create a superuser account."""

    # Prompt for missing values
    if not email:
        email = typer.prompt("Email")
    if not password:
        password = typer.prompt("Password", hide_input=True)
    if not first_name:
        first_name = typer.prompt("First name")
    if not last_name:
        last_name = typer.prompt("Last name")

    asyncio.run(_create_superuser(email, password, first_name, last_name))


async def _create_superuser(email: str, password: str, first_name: str, last_name: str):
    """Async function to create superuser."""
    try:
        async with get_db_session() as db:
            user_repo = UserRepository(db)
            auth_service = AuthService(user_repo)

            # Check if user already exists
            existing_user = await user_repo.get_user_by_email(email)
            if existing_user:
                typer.echo(f"❌ User with email '{email}' already exists!", err=True)
                return

            # Create superuser data
            full_name = f"{first_name} {last_name}".strip()
            user_data = UserCreate(
                email=email,
                password=password,
                full_name=full_name,
                role="admin",  # Set as admin role
                is_email_verified=True,  # Auto-verify superuser email
            )

            # Create the superuser
            superuser = await auth_service.create_user(user_data, is_superuser=True)

            typer.echo("✅ Superuser created successfully!")
            typer.echo(f"📧 Email: {superuser.email}")
            typer.echo(f"👤 Name: {superuser.full_name}")
            typer.echo(f"🔐 Role: {superuser.role}")
            typer.echo(f"✉️  Email Verified: {superuser.is_email_verified}")

    except Exception as e:
        typer.echo(f"❌ Error creating superuser: {str(e)}", err=True)
        raise typer.Exit(1)


@app.command()
def list_users():
    """List all users in the system."""
    asyncio.run(_list_users())


async def _list_users():
    """Async function to list users."""
    try:
        async with get_db_session() as db:
            user_repo = UserRepository(db)
            users = await user_repo.list_users()

            if not users:
                typer.echo("No users found.")
                return

            typer.echo("\n📋 Users in the system:")
            typer.echo("-" * 80)

            for user in users:
                status = "✅ Active" if user.is_active else "❌ Inactive"
                verified = "✅ Verified" if user.is_email_verified else "❌ Unverified"

                typer.echo(f"👤 {user.full_name or 'No name'}")
                typer.echo(f"   📧 {user.email}")
                typer.echo(f"   🔐 Role: {user.role}")
                typer.echo(f"   📊 Status: {status}")
                typer.echo(f"   ✉️  Email: {verified}")
                typer.echo(
                    f"   📅 Created: {user.created_at.strftime('%Y-%m-%d %H:%M:%S')}"
                )
                typer.echo("-" * 80)

    except Exception as e:
        typer.echo(f"❌ Error listing users: {str(e)}", err=True)
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
