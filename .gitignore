# Environment files (except example)
.env
.env.local
.env.production
.env.test

# Database files
*.db
*.sqlite
*.sqlite3

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# pytest
.pytest_cache/

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.orig

# Security and sensitive files
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
.secrets
credentials/
.credentials

# Environment-specific configurations
.env.*
!.env.example
!.env.template

# Database dumps and backups
*.sql
*.dump
dump.rdb

# Jupyter Notebooks (if any)
.ipynb_checkpoints/
*.ipynb

# Package managers
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Redis dump files
dump.rdb

# Alembic (migration tool)
alembic/versions/*
!alembic/versions/.gitkeep

# Local configuration overrides
config.local.py
settings.local.py
local_settings.py

# Profile data
.prof

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# PyCharm
.idea/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# Archive files
*.zip
*.tar.gz
*.rar

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Media uploads (if handling file uploads)
uploads/
media/
static/uploads/

# Backup files from editors
*~
*.swp
*.swo
.#*
#*#