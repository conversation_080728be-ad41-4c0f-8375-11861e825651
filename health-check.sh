#!/bin/bash

# Health check script for <PERSON><PERSON><PERSON> Meat Processing Backend
# This script verifies that all services are running properly

echo "🔍 Checking Fehadan Meat Processing Backend Health..."
echo "=" .= "=========================================="

# Check if Docker Compose is running
if ! docker-compose -f docker-compose.local.yml ps | grep -q "Up"; then
    echo "❌ Services are not running. Start them with: docker-compose -f docker-compose.local.yml up -d"
    exit 1
fi

# Health check function
check_service() {
    local service_name=$1
    local port=$2
    local response=$(curl -s http://localhost:$port/ 2>/dev/null)
    
    if [[ $response == *"healthy"* ]]; then
        echo "✅ $service_name (port $port): healthy"
        return 0
    else
        echo "❌ $service_name (port $port): not responding"
        return 1
    fi
}

echo "🏥 Checking service health endpoints..."

# Check all services
check_service "Auth Service" 8000
check_service "Inventory Service" 8001
check_service "Products Service" 8002
check_service "Orders Service" 8003
check_service "Cart Service" 8004
check_service "Payments Service" 8005
check_service "Notifications Service" 8006
check_service "Reporting Service" 8007

echo ""
echo "🗄️  Checking database connection..."

# Check database
db_check=$(docker exec postgres_db psql -U postgres -d fehadan_db -c "SELECT 'OK' as status;" 2>/dev/null | grep "OK")
if [[ $db_check == *"OK"* ]]; then
    echo "✅ PostgreSQL Database: connected"
else
    echo "❌ PostgreSQL Database: connection failed"
fi

# Check Redis
redis_check=$(docker exec redis redis-cli ping 2>/dev/null)
if [[ $redis_check == "PONG" ]]; then
    echo "✅ Redis Cache: connected"
else
    echo "❌ Redis Cache: connection failed"
fi

echo ""
echo "📊 Service URLs:"
echo "• Auth Service: http://localhost:8000/docs"
echo "• Inventory Service: http://localhost:8001/docs"
echo "• Products Service: http://localhost:8002/docs"
echo "• Orders Service: http://localhost:8003/docs"
echo "• Cart Service: http://localhost:8004/docs"
echo "• Payments Service: http://localhost:8005/docs"
echo "• Notifications Service: http://localhost:8006/docs"
echo "• Reporting Service: http://localhost:8007/docs"

echo ""
echo "🎉 All systems operational!"
echo "🚀 Your Fehadan Meat Processing Backend is ready for development!"