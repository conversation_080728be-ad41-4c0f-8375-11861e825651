from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from shared.config import settings
from shared.database import engine, Base
from datetime import datetime
from sqlalchemy import text

app = FastAPI(title="Cart Microservice")

# CORS setup (adjust origins as needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Create database tables
@app.on_event("startup")
async def startup():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


# Health check endpoint
@app.get("/")
async def health_check():
    """Health check endpoint that verifies service and database connectivity."""
    timestamp = datetime.utcnow().isoformat() + "Z"

    try:
        # Test database connection
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

        return {
            "status": "healthy",
            "service": "cart-microservice",
            "version": "1.0.0",
            "database": "connected",
            "timestamp": timestamp,
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "cart-microservice",
            "version": "1.0.0",
            "database": "disconnected",
            "error": str(e),
            "timestamp": timestamp,
        }


# Future cart routes can be added here
# app.include_router(cart_router, prefix="/cart", tags=["cart"])
