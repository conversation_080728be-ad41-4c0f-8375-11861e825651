from pydantic import BaseModel, EmailStr
from typing import Optional


class UserCreate(BaseModel):
    email: EmailStr
    password: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[str] = "customer"
    is_email_verified: Optional[bool] = False


class UserCompleteRegistration(BaseModel):
    """Schema for completing registration with password and optional details."""

    email: EmailStr
    password: str
    full_name: Optional[str] = None
    role: Optional[str] = "customer"


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class UserResponse(BaseModel):
    id: int
    email: EmailStr
    is_active: bool
    is_email_verified: bool
    role: str
    full_name: Optional[str] = None

    class Config:
        from_attributes = True


class TokenResponse(BaseModel):
    access: str
    refresh: str


class Token(BaseModel):
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"


class PasswordReset(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str


class UserRoleUpdate(BaseModel):
    role: str


class EmailVerificationRequest(BaseModel):
    """Schema for email verification request with password setup."""

    token: str
    password: str
    full_name: Optional[str] = None


class EmailVerificationResponse(BaseModel):
    """Schema for email verification response with automatic login."""

    message: str
    email_verified: bool
    access_token: str
    refresh_token: str
    user: UserResponse


class RegistrationErrorResponse(BaseModel):
    """Schema for registration error response when email fails but account is created."""

    access: str
    refresh: str
    user: UserResponse
    message: str
