from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
import datetime
from typing import Optional, List

from auth_service.models import PasswordReset, User, UserRole, EmailVerification
from auth_service.schemas import UserRoleUpdate


class UserRepository:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_user(self, user_data: dict) -> User:
        """Create a new user in the database."""
        db_user = User(**user_data)
        self.db.add(db_user)
        await self.db.commit()
        await self.db.refresh(db_user)
        return db_user

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Retrieve a user by email."""
        result = await self.db.execute(select(User).filter_by(email=email))
        return result.scalar_one_or_none()

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Retrieve a user by ID."""
        result = await self.db.execute(select(User).filter_by(id=user_id))
        return result.scalar_one_or_none()

    async def update_user(self, user_id: int, user_data: dict) -> Optional[User]:
        """Update user details."""
        await self.db.execute(
            update(User).where(User.id == user_id).values(**user_data)
        )
        await self.db.commit()
        return await self.get_user_by_id(user_id)

    async def delete_user(self, user_id: int) -> bool:
        """Deactivate a user (soft delete)."""
        await self.db.execute(
            update(User).where(User.id == user_id).values(is_active=False)
        )
        await self.db.commit()
        return True

    async def create_user_role(self, user_id: int, role: str) -> UserRole:
        """Assign a role to a user."""
        db_role = UserRole(user_id=user_id, role_name=role)
        self.db.add(db_role)
        await self.db.commit()
        await self.db.refresh(db_role)
        return db_role

    async def update_user_role(
        self, user_id: int, role_update: UserRoleUpdate
    ) -> Optional[User]:
        """Update a user's role."""
        await self.db.execute(
            update(User).where(User.id == user_id).values(role=role_update.role)
        )
        await self.db.commit()
        return await self.get_user_by_id(user_id)

    async def create_password_reset(
        self, user_id: int, token: str, expires_at: datetime.datetime
    ) -> PasswordReset:
        """Create a password reset token."""
        db_reset = PasswordReset(user_id=user_id, token=token, expires_at=expires_at)
        self.db.add(db_reset)
        await self.db.commit()
        await self.db.refresh(db_reset)
        return db_reset

    async def get_password_reset_by_token(self, token: str) -> Optional[PasswordReset]:
        """Retrieve a password reset token."""
        result = await self.db.execute(select(PasswordReset).filter_by(token=token))
        return result.scalar_one_or_none()

    async def create_email_verification(
        self, user_id: int, token: str, expires_at: datetime.datetime
    ) -> EmailVerification:
        """Create an email verification token."""
        db_verification = EmailVerification(
            user_id=user_id, token=token, expires_at=expires_at
        )
        self.db.add(db_verification)
        await self.db.commit()
        await self.db.refresh(db_verification)
        return db_verification

    async def get_email_verification_by_token(
        self, token: str
    ) -> Optional[EmailVerification]:
        """Retrieve an email verification token."""
        result = await self.db.execute(
            select(EmailVerification).filter_by(token=token, is_used=False)
        )
        return result.scalar_one_or_none()

    async def mark_email_verification_used(self, token: str) -> bool:
        """Mark an email verification token as used."""
        await self.db.execute(
            update(EmailVerification)
            .where(EmailVerification.token == token)
            .values(is_used=True)
        )
        await self.db.commit()
        return True

    async def verify_user_email(self, user_id: int) -> bool:
        """Mark user's email as verified."""
        await self.db.execute(
            update(User).where(User.id == user_id).values(is_email_verified=True)
        )
        await self.db.commit()
        return True

    async def list_users(self, limit: int = 100, offset: int = 0) -> List[User]:
        """
        Get a list of all users.

        Args:
            limit: Maximum number of users to return
            offset: Number of users to skip

        Returns:
            List of user instances
        """
        result = await self.db.execute(
            select(User).order_by(User.created_at.desc()).limit(limit).offset(offset)
        )
        return list(result.scalars().all())
