from fastapi import HTTPException, status
from auth_service.repositories.user_repository import UserRepository
from auth_service.schemas import UserResponse, UserRoleUpdate


class UserService:
    def __init__(self, user_repo: UserRepository):
        self.user_repo = user_repo

    async def get_user_profile(self, user_id: int) -> UserResponse:
        """Get user profile by ID."""
        db_user = await self.user_repo.get_user_by_id(user_id)
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")

        return UserResponse.from_orm(db_user)

    async def update_user_role(
        self, user_id: int, role_update: UserRoleUpdate
    ) -> UserResponse:
        """Update a user's role (admin only)."""
        db_user = await self.user_repo.get_user_by_id(user_id)
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")

        updated_user = await self.user_repo.update_user_role(user_id, role_update)
        return UserResponse.from_orm(updated_user)

    async def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user account."""
        return await self.user_repo.delete_user(user_id)
