from shared.core.security import (
    hash_password,
    verify_password,
    create_access_token,
    create_refresh_token,
    verify_token,
)
from shared.config import settings
from fastapi import HTTPEx<PERSON>, status
from datetime import datetime, timedelta
import uuid
from jose import JW<PERSON>rror
from sqlalchemy import delete

from shared.core.utils.generate_token import TokenGenerator
from auth_service.repositories.user_repository import UserRepository
from auth_service.schemas import (
    PasswordReset,
    Token,
    TokenResponse,
    UserCreate,
    UserCompleteRegistration,
    UserLogin,
    UserResponse,
    UserRoleUpdate,
    EmailVerificationRequest,
    EmailVerificationResponse,
)
from auth_service.models import PasswordReset as PasswordResetModel
from auth_service.utils.emails import (
    send_verify_email,
    generate_verification_token,
    get_verification_token_expiry,
)


class AuthService:
    def __init__(self, user_repo: UserRepository):
        self.user_repo = user_repo
        self.token_generator = TokenGenerator()

    async def register_user(self, user: UserCreate) -> TokenResponse:
        """Register a new user with email only and return JWT tokens."""
        # Check if email already exists
        existing_user = await self.user_repo.get_user_by_email(user.email)
        if existing_user:
            raise HTTPException(status_code=400, detail="Email already registered")

        # Create user with only email
        user_data = {
            "email": user.email,
            "hashed_password": None,
            "is_email_verified": False,
            "is_active": True,
        }

        # Create user in database
        db_user = await self.user_repo.create_user(user_data)

        # Generate verification token
        verification_token = generate_verification_token()
        expires_at = get_verification_token_expiry()

        # Store verification token
        await self.user_repo.create_email_verification(
            db_user.id, verification_token, expires_at
        )

        # Send verification email
        email_sent = await send_verify_email(
            db_user.email, verification_token, db_user.full_name
        )

        # Generate JWT tokens
        access_token = create_access_token({"sub": db_user.email})
        refresh_token = create_refresh_token({"sub": db_user.email})

        if not email_sent:
            # Email failed but user account was created successfully
            # Return 409 (Conflict) with tokens so user can still access their account

            # Create response with tokens and user data
            response_data = {
                "access": access_token,
                "refresh": refresh_token,
                "user": {
                    "id": db_user.id,
                    "email": db_user.email,
                    "full_name": db_user.full_name,
                    "is_email_verified": db_user.is_email_verified,
                    "is_active": db_user.is_active,
                    "role": db_user.role,
                    "created_at": (
                        db_user.created_at.isoformat() if db_user.created_at else None
                    ),
                },
                "message": "Account created successfully, but verification email could not be sent. You can still access your account and request email verification later.",
            }

            raise HTTPException(status_code=409, detail=response_data)

        return TokenResponse(access=access_token, refresh=refresh_token)

    async def login_user(self, user: UserLogin) -> TokenResponse:
        """Authenticate a user and return JWT tokens."""
        db_user = await self.user_repo.get_user_by_email(user.email)
        if not db_user or not verify_password(user.password, db_user.hashed_password):
            raise HTTPException(status_code=401, detail="Invalid credentials")

        if not db_user.is_active:
            raise HTTPException(status_code=401, detail="Account is deactivated")

        access_token = create_access_token({"sub": db_user.email})
        refresh_token = create_refresh_token({"sub": db_user.email})

        return TokenResponse(access=access_token, refresh=refresh_token)

    async def refresh_token(self, refresh_token: str) -> Token:
        """Refresh an access token using a refresh token."""
        try:
            payload = verify_token(refresh_token)
            email = payload.get("sub")
            if not email:
                raise HTTPException(status_code=401, detail="Invalid refresh token")

            db_user = await self.user_repo.get_user_by_email(email)
            if not db_user or not db_user.is_active:
                raise HTTPException(
                    status_code=401, detail="User not found or inactive"
                )

            access_token = create_access_token({"sub": db_user.email})
            return Token(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
            )
        except JWTError:
            raise HTTPException(status_code=401, detail="Invalid refresh token")

    async def request_password_reset(self, reset_data: PasswordReset) -> None:
        """Create a password reset token and store it."""
        db_user = await self.user_repo.get_user_by_email(reset_data.email)
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")

        token = str(uuid.uuid4())
        expires_at = datetime.utcnow() + timedelta(
            hours=settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS
        )
        await self.user_repo.create_password_reset(db_user.id, token, expires_at)
        # Note: Email sending would be handled by a Celery task (set up in Week 6)

    async def confirm_password_reset(self, token: str, new_password: str) -> None:
        """Confirm password reset and update the user's password."""
        reset_record = await self.user_repo.get_password_reset_by_token(token)
        if not reset_record or reset_record.expires_at < datetime.utcnow():
            raise HTTPException(
                status_code=400, detail="Invalid or expired reset token"
            )

        hashed_password = hash_password(new_password)
        await self.user_repo.update_user(
            reset_record.user_id, {"hashed_password": hashed_password}
        )
        # Delete the used reset token
        await self.user_repo.db.execute(
            delete(PasswordResetModel).where(PasswordResetModel.token == token)
        )
        await self.user_repo.db.commit()

    async def verify_email(
        self, request: EmailVerificationRequest
    ) -> EmailVerificationResponse:
        """Verify user's email address and set password using verification token."""
        verification = await self.user_repo.get_email_verification_by_token(
            request.token
        )

        if not verification:
            raise HTTPException(status_code=400, detail="Invalid verification token")

        if verification.expires_at < datetime.utcnow():
            raise HTTPException(
                status_code=400, detail="Verification token has expired"
            )

        if verification.is_used:
            raise HTTPException(
                status_code=400, detail="Verification token has already been used"
            )

        # Get the user
        user = await self.user_repo.get_user_by_id(verification.user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if password is already set
        if user.hashed_password:
            raise HTTPException(
                status_code=400, detail="Password is already set for this user"
            )

        # Hash the password and update user
        hashed_password = hash_password(request.password)
        update_data = {"hashed_password": hashed_password, "is_email_verified": True}

        if request.full_name:
            update_data["full_name"] = request.full_name

        # Update user with password and email verification
        updated_user = await self.user_repo.update_user(
            verification.user_id, update_data
        )

        # Mark token as used
        await self.user_repo.mark_email_verification_used(request.token)

        # Generate JWT tokens for automatic login
        access_token = create_access_token({"sub": updated_user.email})
        refresh_token = create_refresh_token({"sub": updated_user.email})

        return EmailVerificationResponse(
            message="Email successfully verified and password set! You are now logged in.",
            email_verified=True,
            access_token=access_token,
            refresh_token=refresh_token,
            user=UserResponse.model_validate(updated_user),
        )

    async def create_user(self, user_data: UserCreate, is_superuser: bool = False):
        """
        Create a new user account.

        Args:
            user_data: User creation data
            is_superuser: Whether to create as superuser (skips email verification)

        Returns:
            Created user instance

        Raises:
            HTTPException: If email is already registered or validation fails
        """
        # Check if user already exists
        existing_user = await self.user_repo.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(status_code=400, detail="Email already registered")

        # Hash password
        hashed_password = hash_password(user_data.password)

        # Create user data dictionary
        user_dict = {
            "email": user_data.email,
            "hashed_password": hashed_password,
            "full_name": user_data.full_name,
            "role": user_data.role,
            "is_email_verified": is_superuser
            or getattr(user_data, "is_email_verified", False),
            "is_active": True,
        }

        # Create user in database
        created_user = await self.user_repo.create_user(user_dict)

        # Send verification email only if not superuser and not already verified
        if not is_superuser and not getattr(user_data, "is_email_verified", False):
            verification_token = generate_verification_token()
            expires_at = get_verification_token_expiry()

            # Store verification token
            await self.user_repo.create_email_verification(
                created_user.id, verification_token, expires_at
            )

            # Send verification email
            await send_verify_email(
                created_user.email, verification_token, created_user.full_name
            )

        return created_user
