"""
Email utilities for authentication module.

This module provides email sending functionality specifically for authentication
workflows like email verification, password reset, and welcome emails.
"""

import os
import secrets
import datetime
from pathlib import Path
from typing import Optional
from shared.core.utils.email import EmailClient, EmailError
from shared.config import settings


async def send_verify_email(
    email: str, verification_token: str, first_name: Optional[str] = None
) -> bool:
    """
    Send email verification email to user.

    Args:
        email: Recipient email address
        verification_token: Verification token to include in the email
        first_name: User's first name (optional, defaults to email username)

    Returns:
        True if email was sent successfully, False otherwise
    """
    try:
        # Initialize email client
        email_client = EmailClient()

        # Get template path
        template_path = (
            Path(__file__).parent.parent / "email_templates" / "email_verification.html"
        )

        # Extract first name from email if not provided
        if not first_name:
            first_name = email.split("@")[0].title()

        # Create verification link
        # In production, this should be your frontend URL
        base_url = getattr(settings, "FRONTEND_URL", "https://fehadan.com")
        verification_link = f"{base_url}/auth/verify-email?token={verification_token}"

        # Template variables
        template_vars = {
            "first_name": first_name,
            "email": email,
            "verification_link": verification_link,
            "verification_token": verification_token,
            "expires_in_hours": "24",
            "current_year": datetime.datetime.now().year,
        }

        # Format email body using the template
        html_body = await email_client.format_email_body(
            str(template_path), template_vars
        )

        # Create plain text version
        text_body = f"""
        Welcome to Fehadan Meat Processing!
        
        Hello {first_name},
        
        Thank you for registering with Fehadan Meat Processing! 
        
        To complete your registration, please verify your email address by visiting:
        {verification_link}
        
        This verification link will expire in 24 hours for your security.
        
        If you didn't create an account with us, please ignore this email.
        
        Best regards,
        The Fehadan Meat Processing Team
        
        © {datetime.datetime.now().year} Fehadan Meat Processing. All rights reserved.
        """

        # Send the email
        response = await email_client.send_email(
            to_email=email,
            subject="🎉 Welcome to Fehadan - Please Verify Your Email",
            html_body=html_body,
            text_body=text_body,
            tag="email_verification",
            metadata={
                "email_type": "verification",
                "user_email": email,
                "verification_token": verification_token,
                "sent_at": datetime.datetime.now().isoformat(),
            },
        )

        return True

    except EmailError as e:
        print(f"Failed to send verification email to {email}: {e}")
        return False
    except FileNotFoundError as e:
        print(f"Email template not found: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error sending verification email to {email}: {e}")
        return False


def generate_verification_token() -> str:
    """
    Generate a secure verification token.

    Returns:
        A secure random token string
    """
    return secrets.token_urlsafe(32)


def get_verification_token_expiry() -> datetime.datetime:
    """
    Get the expiry datetime for verification tokens.

    Returns:
        Datetime object 24 hours from now
    """
    return datetime.datetime.utcnow() + datetime.timedelta(hours=24)
