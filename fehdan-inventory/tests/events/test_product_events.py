"""Test product events: product_created, product_updated, product_deleted"""

import asyncio
import random
from typing import Dict, Any
from app.events.schemas import ProductEventType
from tests.events.base_test import BaseEventTest


class TestProductEvents(BaseEventTest):
    """Test class for product-related events."""

    def create_product_event(
        self, event_type: str, product_id: int, **kwargs
    ) -> Dict[str, Any]:
        """Create product event with base data."""
        event = self.create_base_event(event_type, "product_service")
        event["data"] = {"product_id": product_id, **kwargs}
        return event

    async def test_product_created_event(self) -> bool:
        """Test product created event creates inventory item."""
        product_id = random.randint(10000, 99999)

        event_data = self.create_product_event(
            ProductEventType.PRODUCT_CREATED,
            product_id,
            name=f"Test Beef {product_id}",
            description="Test product",
            category="meat",
            price=15.99,
            weight=1.0,
            unit="kg",
            created_by=1,
            status="active",
        )

        await self.consumer.handle_product_created(event_data)
        inventory_item = await self.find_inventory_item_by_product(product_id)
        return inventory_item is not None and inventory_item.product_id == product_id

    async def test_product_updated_event(self) -> bool:
        """Test product updated event doesn't affect inventory."""
        product_id = random.randint(10000, 99999)

        # Create product first
        create_event = self.create_product_event(
            ProductEventType.PRODUCT_CREATED,
            product_id,
            name=f"Test Product {product_id}",
            category="meat",
            price=15.99,
        )
        await self.consumer.handle_product_created(create_event)

        initial_count = await self.count_inventory_items_for_product(product_id)

        # Update product
        update_event = self.create_product_event(
            ProductEventType.PRODUCT_UPDATED,
            product_id,
            name="Updated Product",
            price=18.99,
        )
        await self.consumer.handle_product_updated(update_event)

        final_count = await self.count_inventory_items_for_product(product_id)
        return initial_count == final_count

    async def test_product_deleted_event(self) -> bool:
        """Test product deleted event deactivates inventory item."""
        product_id = random.randint(10000, 99999)

        # Create product first
        create_event = self.create_product_event(
            ProductEventType.PRODUCT_CREATED,
            product_id,
            name=f"Test Product {product_id}",
            category="meat",
        )
        await self.consumer.handle_product_created(create_event)

        # Delete product
        delete_event = self.create_product_event(
            ProductEventType.PRODUCT_DELETED,
            product_id,
            deleted_by=1,
            reason="Test deletion",
        )
        await self.consumer.handle_product_deleted(delete_event)

        final_item = await self.find_inventory_item_by_product(product_id)
        return final_item is not None and not final_item.is_active

    async def run_all_product_tests(self) -> bool:
        """Run all product event tests."""
        await self.setup_database()

        tests = [
            self.test_product_created_event(),
            self.test_product_updated_event(),
            self.test_product_deleted_event(),
        ]

        results = await asyncio.gather(*tests, return_exceptions=True)
        passed = sum(1 for r in results if r is True)

        print(f"Product tests: {passed}/{len(tests)} passed")
        return passed == len(tests)


async def main():
    """Run product event tests."""
    test = TestProductEvents()
    success = await test.run_all_product_tests()
    print("✅ PASSED" if success else "❌ FAILED")
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    if not result:
        exit(1)
