"""
Base test utilities and fixtures for event testing.
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from app.core.database import get_db, create_tables
from app.events.kafka_config import KafkaConfig
from app.events.consumer import EventConsumer
from app.services.inventory_item import InventoryService
from app.schemas import InventoryItemResponse

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BaseEventTest:
    """Base class for event testing with common utilities."""

    def __init__(self):
        self.kafka_config = KafkaConfig()
        self.consumer = EventConsumer()

    async def setup_database(self):
        """Setup database tables for testing."""
        await create_tables()
        logger.info("✅ Database tables ready")

    async def get_inventory_service(self) -> InventoryService:
        """Get a fresh inventory service instance."""
        db = await get_db().__anext__()
        return InventoryService(db)

    async def find_inventory_item_by_product(
        self, product_id: int, timeout: int = 5
    ) -> Optional[InventoryItemResponse]:
        """Find inventory item by product ID with timeout."""
        start_time = asyncio.get_event_loop().time()
        inventory_service = await self.get_inventory_service()

        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                items = await inventory_service.list_inventory_items(
                    product_id=product_id
                )
                if items:
                    return items[0]
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.debug(f"Error searching for inventory item: {e}")
                await asyncio.sleep(0.1)

        return None

    async def count_inventory_items_for_product(self, product_id: int) -> int:
        """Count inventory items for a specific product."""
        inventory_service = await self.get_inventory_service()
        items = await inventory_service.list_inventory_items(product_id=product_id)
        return len(items)

    def create_base_event(
        self, event_type: str, service: str = "test_service"
    ) -> Dict[str, Any]:
        """Create base event structure."""
        return {
            "event_id": str(uuid.uuid4()),
            "event_type": event_type,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "service": service,
            "version": "1.0",
            "data": {},
        }

    async def verify_inventory_item_data(
        self,
        inventory_item: InventoryItemResponse,
        expected_product_id: int,
        expected_location_id: int = 1,
        expected_quantity: int = 0,
        expected_created_by: int = 0,
    ) -> bool:
        """Verify inventory item has expected data."""
        checks = []

        # Check product ID
        if inventory_item.product_id == expected_product_id:
            logger.info(f"✅ Product ID correct: {inventory_item.product_id}")
            checks.append(True)
        else:
            logger.error(
                f"❌ Product ID wrong: expected {expected_product_id}, got {inventory_item.product_id}"
            )
            checks.append(False)

        # Check location ID
        if inventory_item.location_id == expected_location_id:
            logger.info(f"✅ Location ID correct: {inventory_item.location_id}")
            checks.append(True)
        else:
            logger.error(
                f"❌ Location ID wrong: expected {expected_location_id}, got {inventory_item.location_id}"
            )
            checks.append(False)

        # Check quantity
        if inventory_item.quantity_on_hand == expected_quantity:
            logger.info(f"✅ Quantity correct: {inventory_item.quantity_on_hand}")
            checks.append(True)
        else:
            logger.error(
                f"❌ Quantity wrong: expected {expected_quantity}, got {inventory_item.quantity_on_hand}"
            )
            checks.append(False)

        # Check created by
        if inventory_item.created_by == expected_created_by:
            logger.info(f"✅ Created by correct: {inventory_item.created_by}")
            checks.append(True)
        else:
            logger.error(
                f"❌ Created by wrong: expected {expected_created_by}, got {inventory_item.created_by}"
            )
            checks.append(False)

        # Check if active
        if inventory_item.is_active:
            logger.info(f"✅ Is active: {inventory_item.is_active}")
            checks.append(True)
        else:
            logger.error(f"❌ Not active: {inventory_item.is_active}")
            checks.append(False)

        success = all(checks)
        if success:
            logger.info(f"🎉 All checks passed for inventory item {inventory_item.id}")
        else:
            logger.error(
                f"❌ Some checks failed for inventory item {inventory_item.id}"
            )

        return success
