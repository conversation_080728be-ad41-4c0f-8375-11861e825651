#!/usr/bin/env python3
"""Simple event flow test - validates product creation triggers inventory item creation."""

import asyncio
import json
import uuid
from confluent_kafka import Producer
from app.core.database import get_db, create_tables
from app.events.kafka_config import kafka_config
from app.events.consumer import EventConsumer
from app.events.schemas import ProductEventType
from app.services.inventory_item import InventoryService


class SimpleEventFlowTest:
    """Minimal event flow test."""

    async def test_event_flow(self) -> bool:
        """Test complete event flow."""
        product_id = 12345

        # Setup
        await create_tables()

        # Create event
        event_data = {
            "event_id": str(uuid.uuid4()),
            "event_type": ProductEventType.PRODUCT_CREATED,
            "timestamp": "2025-09-22T09:30:00Z",
            "service": "product_service",
            "version": "1.0",
            "data": {
                "product_id": product_id,
                "name": "Test Steak",
                "description": "Test product",
                "category": "meat",
                "price": 15.99,
                "weight": 1.0,
                "unit": "kg",
                "created_by": 1,
                "status": "active",
            },
        }

        # Test producer
        producer = Producer(kafka_config.get_producer_config())
        producer.produce(
            kafka_config.topics["products"],
            value=json.dumps(event_data),
            key=str(product_id),
        )
        producer.flush()

        # Test handler
        consumer = EventConsumer()
        await consumer.handle_product_created(event_data)

        # Verify inventory item created
        db = await get_db().__anext__()
        inventory_service = InventoryService(db)
        items = await inventory_service.list_inventory_items(product_id=product_id)

        return len(items) > 0 and items[0].product_id == product_id


async def main():
    """Run test."""
    test = SimpleEventFlowTest()
    success = await test.test_event_flow()
    print("✅ PASSED" if success else "❌ FAILED")
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    if not result:
        exit(1)
