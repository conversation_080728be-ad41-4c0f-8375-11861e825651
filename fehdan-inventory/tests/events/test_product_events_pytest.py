"""Pytest-based tests for product events."""

import pytest
import uuid
from datetime import datetime
from app.events.schemas import ProductEventType


class TestProductEvents:
    """Test class for product-related events using pytest."""

    def create_product_event(self, event_type: str, product_data: dict):
        """Helper to create product events."""
        return {
            "event_id": str(uuid.uuid4()),
            "event_type": event_type,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "service": "product_service",
            "version": "1.0",
            "data": product_data,
        }

    @pytest.mark.asyncio
    async def test_product_created_event_creates_inventory_item(
        self, event_consumer, inventory_service, sample_product_data
    ):
        """Test that product created event creates an inventory item."""
        product_event = self.create_product_event(
            ProductEventType.PRODUCT_CREATED, sample_product_data
        )

        await event_consumer.handle_product_created(product_event)

        inventory_items = await inventory_service.list_inventory_items(
            product_id=sample_product_data["product_id"]
        )

        assert len(inventory_items) == 1
        assert inventory_items[0].product_id == sample_product_data["product_id"]
        assert inventory_items[0].location_id == 1
        assert inventory_items[0].quantity_on_hand == 0
        assert inventory_items[0].is_active is True

    @pytest.mark.asyncio
    async def test_product_created_event_duplicate_product(
        self, event_consumer, inventory_service, sample_product_data
    ):
        """Test that duplicate product created events don't create multiple inventory items."""
        product_event = self.create_product_event(
            ProductEventType.PRODUCT_CREATED, sample_product_data
        )

        await event_consumer.handle_product_created(product_event)
        await event_consumer.handle_product_created(product_event)

        inventory_items = await inventory_service.list_inventory_items(
            product_id=sample_product_data["product_id"]
        )
        assert len(inventory_items) == 1

    @pytest.mark.asyncio
    async def test_product_updated_event(
        self, event_consumer, inventory_service, sample_product_data
    ):
        """Test that product updated event doesn't affect inventory count."""
        create_event = self.create_product_event(
            ProductEventType.PRODUCT_CREATED, sample_product_data
        )
        await event_consumer.handle_product_created(create_event)

        initial_count = len(
            await inventory_service.list_inventory_items(
                product_id=sample_product_data["product_id"]
            )
        )

        updated_data = {
            **sample_product_data,
            "name": "Updated Test Beef",
            "price": 29.99,
        }
        update_event = self.create_product_event(
            ProductEventType.PRODUCT_UPDATED, updated_data
        )
        await event_consumer.handle_product_updated(update_event)

        final_count = len(
            await inventory_service.list_inventory_items(
                product_id=sample_product_data["product_id"]
            )
        )
        assert final_count == initial_count

    @pytest.mark.asyncio
    async def test_product_deleted_event_deactivates_inventory(
        self, event_consumer, inventory_service, sample_product_data
    ):
        """Test that product deleted event deactivates inventory item."""
        create_event = self.create_product_event(
            ProductEventType.PRODUCT_CREATED, sample_product_data
        )
        await event_consumer.handle_product_created(create_event)

        delete_event = self.create_product_event(
            ProductEventType.PRODUCT_DELETED,
            {"product_id": sample_product_data["product_id"], "deleted_by": 1},
        )
        await event_consumer.handle_product_deleted(delete_event)

        # Event processed without exception
        assert True
