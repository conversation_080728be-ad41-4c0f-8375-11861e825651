"""Test order events: order_placed, order_confirmed, order_cancelled, order_deleted"""

import asyncio
import random
from typing import Dict, Any, List
from app.events.schemas import OrderEventType
from tests.events.base_test import BaseEventTest


class TestOrderEvents(BaseEventTest):
    """Test class for order-related events."""

    def create_order_event(
        self, event_type: str, order_id: int, **kwargs
    ) -> Dict[str, Any]:
        """Create order event with base data."""
        event = self.create_base_event(event_type, "order_service")
        event["data"] = {"order_id": order_id, **kwargs}
        return event

    async def test_order_created_event(self) -> bool:
        """Test order created event."""
        order_id = random.randint(1000, 9999)
        items = [{"product_id": 12345, "quantity": 5, "price": 12.99}]

        event = self.create_order_event(
            OrderEventType.ORDER_CREATED,
            order_id,
            customer_id=1,
            status="pending",
            items=items,
            total_amount=64.95,
        )

        await self.consumer.handle_order_created(event)
        return True

    async def test_order_cancelled_event(self) -> bool:
        """Test order cancelled event."""
        order_id = random.randint(1000, 9999)

        event = self.create_order_event(
            OrderEventType.ORDER_CANCELLED,
            order_id,
            reason="Customer request",
            cancelled_by=1,
        )

        await self.consumer.handle_order_cancelled(event)
        return True

    async def run_all_order_tests(self) -> bool:
        """Run all order event tests."""
        await self.setup_database()

        tests = [
            self.test_order_created_event(),
            self.test_order_cancelled_event(),
        ]

        results = await asyncio.gather(*tests, return_exceptions=True)
        passed = sum(1 for r in results if r is True)

        print(f"Order tests: {passed}/{len(tests)} passed")
        return passed == len(tests)


async def main():
    """Run order event tests."""
    test = TestOrderEvents()
    success = await test.run_all_order_tests()
    print("✅ PASSED" if success else "❌ FAILED")
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    if not result:
        exit(1)
