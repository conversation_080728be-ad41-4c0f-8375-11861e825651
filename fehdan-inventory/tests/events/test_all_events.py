"""
Comprehensive Event Test Runner

Runs all event tests:
- Product events: product_created, product_updated, product_deleted
- Order events: order_placed, order_confirmed, order_cancelled, order_deleted
"""

import asyncio
import logging
import sys
from typing import List, Tuple

from tests.events.test_product_events import TestProductEvents
from tests.events.test_order_events import TestOrderEvents

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class EventTestRunner:
    """Main test runner for all event tests."""

    def __init__(self):
        self.results: List[Tuple[str, bool]] = []

    async def run_product_tests(self) -> bool:
        """Run all product event tests."""
        logger.info("🧪 Running Product Event Tests")
        test = TestProductEvents()
        success = await test.run_all_product_tests()
        self.results.append(("Product Events", success))
        return success

    async def run_order_tests(self) -> bool:
        """Run all order event tests."""
        logger.info("🧪 Running Order Event Tests")
        test = TestOrderEvents()
        success = await test.run_all_order_tests()
        self.results.append(("Order Events", success))
        return success

    async def run_all_tests(self) -> bool:
        """Run all event tests."""
        logger.info("🚀 Starting Complete Event Test Suite")
        logger.info("=" * 80)

        # Run product tests
        logger.info("\n" + "🔥" * 30 + " PRODUCT EVENTS " + "🔥" * 30)
        await self.run_product_tests()

        # Run order tests
        logger.info("\n" + "🔥" * 30 + " ORDER EVENTS " + "🔥" * 30)
        await self.run_order_tests()

        # Print summary
        self.print_final_summary()

        # Return overall success
        return all(result[1] for result in self.results)

    def print_final_summary(self):
        """Print final test summary."""
        logger.info("\n" + "=" * 80)
        logger.info("📊 FINAL TEST SUMMARY")
        logger.info("=" * 80)

        total_tests = len(self.results)
        passed_tests = sum(1 for _, success in self.results if success)

        for test_name, success in self.results:
            status = "✅ PASSED" if success else "❌ FAILED"
            logger.info(f"   {test_name}: {status}")

        logger.info("-" * 80)
        logger.info(f"   Total: {passed_tests}/{total_tests} test suites passed")

        if passed_tests == total_tests:
            logger.info("🎉 ALL EVENT TESTS PASSED! 🎉")
            logger.info("The event-driven inventory system is working perfectly!")
        else:
            failed_tests = total_tests - passed_tests
            logger.error(f"❌ {failed_tests} TEST SUITE(S) FAILED!")
            logger.error("Some event handlers need attention.")

        logger.info("=" * 80)


async def main():
    """Main function to run all tests."""
    try:
        runner = EventTestRunner()
        success = await runner.run_all_tests()
        return success
    except KeyboardInterrupt:
        logger.info("\n🛑 Tests interrupted by user")
        return False
    except Exception as e:
        logger.error(f"❌ Test runner failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Run the test suite
    result = asyncio.run(main())

    # Exit with appropriate code
    sys.exit(0 if result else 1)
