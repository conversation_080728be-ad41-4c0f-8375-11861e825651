#!/usr/bin/env python3
"""
Test script for the inventory service - testing product creation without events.
"""

import asyncio
import logging
from app.core.database import get_db, create_tables
from app.services.inventory_item import InventoryService, User
from app.schemas import InventoryItemCreate, InventoryItemUpdate

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_inventory_service():
    """Test the inventory service directly."""
    logger.info("🧪 Testing Inventory Service")
    logger.info("=" * 50)

    try:
        # Create database tables
        await create_tables()
        logger.info("✅ Database tables created/verified")

        # Get database connection
        db = await get_db().__anext__()
        logger.info("✅ Database connection established")

        # Create inventory service
        inventory_service = InventoryService(db)
        logger.info("✅ Inventory service created")

        # Create test user
        test_user = User(email="<EMAIL>", id=1)
        logger.info(f"✅ Test user created: {test_user.email}")

        # Create inventory item data (use a unique product ID)
        import random

        test_product_id = random.randint(1000, 9999)

        inventory_data = InventoryItemCreate(
            product_id=test_product_id,  # Random test product ID
            location_id=1,  # Default location
            quantity_on_hand=50,  # Initial stock
            reorder_point=10,
            max_stock_level=1000,
        )
        logger.info(
            f"✅ Inventory data prepared for product {inventory_data.product_id}"
        )

        # Create the inventory item
        logger.info("🔨 Creating inventory item...")
        inventory_item = await inventory_service.create_stock_item(
            item_data=inventory_data,
            current_user=test_user,
            skip_product_validation=True,  # Skip since we don't have product service
            skip_location_validation=True,  # Skip since we might not have locations
        )

        logger.info(f"✅ Successfully created inventory item!")
        logger.info(f"   - ID: {inventory_item.id}")
        logger.info(f"   - Product ID: {inventory_item.product_id}")
        logger.info(f"   - Location ID: {inventory_item.location_id}")
        logger.info(f"   - Quantity: {inventory_item.quantity_on_hand}")
        logger.info(f"   - Available: {inventory_item.available_quantity}")
        logger.info(f"   - Created by: {inventory_item.created_by}")

        # Test retrieving the item
        logger.info("🔍 Retrieving inventory item...")
        retrieved_item = await inventory_service.get_inventory_item(inventory_item.id)
        logger.info(f"✅ Successfully retrieved inventory item {retrieved_item.id}")

        # Test updating the item
        logger.info("✏️ Testing inventory update...")
        update_data = InventoryItemUpdate(quantity_on_hand=75)
        updated_item = await inventory_service.update_inventory_item(
            item_id=inventory_item.id, update_data=update_data, current_user=test_user
        )
        logger.info(
            f"✅ Successfully updated inventory item to quantity {updated_item.quantity_on_hand}"
        )

        # Test listing items
        logger.info("📋 Testing inventory listing...")
        items = await inventory_service.list_inventory_items()
        logger.info(f"✅ Found {len(items)} inventory items")

        logger.info("🎉 All inventory service tests passed!")

        return True

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False

    finally:
        # Close database connection
        if "db" in locals():
            await db.close()


async def test_user_handling():
    """Test different user scenarios."""
    logger.info("\n🧪 Testing User Handling")
    logger.info("=" * 50)

    try:
        # Get database connection
        db = await get_db().__anext__()
        inventory_service = InventoryService(db)

        # Test 1: User with ID
        user_with_id = User(email="<EMAIL>", id=100)
        logger.info(
            f"User with ID - email: {user_with_id.email}, id: {user_with_id.id}"
        )

        # Test 2: User without ID (old style)
        user_no_id = User(email="<EMAIL>")
        logger.info(
            f"User without ID - email: {user_no_id.email}, id: {getattr(user_no_id, 'id', 'None')}"
        )

        # Test 3: System user
        system_user = User(email="<EMAIL>", id=0)
        logger.info(f"System user - email: {system_user.email}, id: {system_user.id}")

        logger.info("✅ User handling tests completed")

    except Exception as e:
        logger.error(f"❌ User handling test failed: {e}")
        return False

    finally:
        if "db" in locals():
            await db.close()


async def main():
    """Main test function."""
    logger.info("🚀 Starting Inventory Service Tests")

    # Test user handling first
    await test_user_handling()

    # Test inventory service
    success = await test_inventory_service()

    if success:
        logger.info("\n🎉 All tests completed successfully!")
    else:
        logger.error("\n❌ Some tests failed!")

    return success


if __name__ == "__main__":
    asyncio.run(main())
