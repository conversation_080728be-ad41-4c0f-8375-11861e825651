"""Pytest-based tests for inventory service."""

import pytest
from app.services.inventory_item import User
from app.schemas import InventoryItemCreate, InventoryItemUpdate


class TestInventoryService:
    """Test class for inventory service using pytest."""

    @pytest.mark.asyncio
    async def test_create_inventory_item(
        self, inventory_service, sample_inventory_data
    ):
        """Test creating an inventory item."""
        inventory_data = InventoryItemCreate(**sample_inventory_data)
        test_user = User(email="<EMAIL>", id=1)

        result = await inventory_service.create_stock_item(
            item_data=inventory_data,
            current_user=test_user,
            skip_product_validation=True,
            skip_location_validation=True,
        )

        assert result.product_id == sample_inventory_data["product_id"]
        assert result.location_id == sample_inventory_data["location_id"]
        assert result.quantity_on_hand == sample_inventory_data["quantity_on_hand"]
        assert result.created_by == test_user.id
        assert result.is_active is True

    @pytest.mark.asyncio
    async def test_get_inventory_item(self, inventory_service, sample_inventory_data):
        """Test retrieving an inventory item."""
        inventory_data = InventoryItemCreate(**sample_inventory_data)
        test_user = User(email="<EMAIL>", id=1)

        created_item = await inventory_service.create_stock_item(
            item_data=inventory_data,
            current_user=test_user,
            skip_product_validation=True,
            skip_location_validation=True,
        )

        retrieved_item = await inventory_service.get_inventory_item(created_item.id)

        assert retrieved_item.id == created_item.id
        assert retrieved_item.product_id == created_item.product_id

    @pytest.mark.asyncio
    async def test_update_inventory_item(
        self, inventory_service, sample_inventory_data
    ):
        """Test updating an inventory item."""
        inventory_data = InventoryItemCreate(**sample_inventory_data)
        test_user = User(email="<EMAIL>", id=1)

        created_item = await inventory_service.create_stock_item(
            item_data=inventory_data,
            current_user=test_user,
            skip_product_validation=True,
            skip_location_validation=True,
        )

        update_data = InventoryItemUpdate(reorder_point=15)
        updated_item = await inventory_service.update_inventory_item(
            item_id=created_item.id,
            update_data=update_data,
            current_user=test_user,
        )

        assert updated_item.reorder_point == 15

    @pytest.mark.asyncio
    async def test_list_inventory_items(self, inventory_service, sample_inventory_data):
        """Test listing inventory items."""
        inventory_data = InventoryItemCreate(**sample_inventory_data)
        test_user = User(email="<EMAIL>", id=1)

        await inventory_service.create_stock_item(
            item_data=inventory_data,
            current_user=test_user,
            skip_product_validation=True,
            skip_location_validation=True,
        )

        items = await inventory_service.list_inventory_items()
        assert len(items) >= 1

    @pytest.mark.asyncio
    async def test_delete_inventory_item(
        self, inventory_service, sample_inventory_data
    ):
        """Test deleting an inventory item."""
        inventory_data = InventoryItemCreate(**sample_inventory_data)
        test_user = User(email="<EMAIL>", id=1)

        created_item = await inventory_service.create_stock_item(
            item_data=inventory_data,
            current_user=test_user,
            skip_product_validation=True,
            skip_location_validation=True,
        )

        result = await inventory_service.delete_inventory_item(
            item_id=created_item.id, current_user=test_user
        )
        assert result is None  # Delete returns None
