import pytest
import httpx
from fastapi import HTT<PERSON>Exception
from fastapi.testclient import TestClient
from main import app
from app.services.auth import get_current_user

try:
    from cryptography.hazmat.primitives import serialization
    from cryptography.hazmat.backends import default_backend

    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False


# Fixtures
@pytest.fixture
def test_client():
    """Fixture to provide a FastAPI test client."""
    return TestClient(app)


@pytest.fixture
def auth_service_url():
    """Base URL for the auth service."""
    return "https://fehdan-auth-service.onrender.com/api/v1"


@pytest.fixture
def test_token():
    """Test JWT token provided by user."""
    return "PASTE_THE_TEST_TOKEN_HERE"


@pytest.mark.asyncio
class TestAuthService:
    """Test class for authentication service integration."""

    async def test_auth_service_jwks(self, auth_service_url):
        """Test fetching JWKS from auth service."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{auth_service_url}/auth/.well-known/jwks.json"
            )
            assert response.status_code == 200
            jwks = response.json()
            assert "public_key" in jwks
            assert isinstance(jwks["public_key"], str)
            assert jwks["public_key"].startswith("-----BEGIN PUBLIC KEY-----")
            assert jwks["public_key"].endswith("-----END PUBLIC KEY-----")
            # Optional: Validate RSA public key format if cryptography is available
            if CRYPTOGRAPHY_AVAILABLE:
                try:
                    public_key = serialization.load_pem_public_key(
                        jwks["public_key"].encode(), backend=default_backend()
                    )
                    assert (
                        public_key.key_size >= 2048
                    )  # Ensure key is sufficiently strong
                except Exception as e:
                    print(f"Failed to parse public key: {e}")
                    raise AssertionError("Invalid RSA public key format")

    async def test_token_introspection(self, auth_service_url, test_token):
        """Test token introspection endpoint."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_service_url}/auth/verify-token",
                headers={"Authorization": f"Bearer {test_token}"},
            )
            assert response.status_code == 200
            result = response.json()
            assert result["active"] is True
            assert result["sub"] == "<EMAIL>"
            assert "customer" in result["roles"]
            assert "read" in result["scope"]
            assert "write" in result["scope"]

    async def test_auth_service_introspection_invalid_token(self, auth_service_url):
        """Test token introspection with an invalid token."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_service_url}/auth/verify-token",
                headers={"Authorization": "Bearer invalid_token"},
            )
            assert response.status_code == 401
            assert response.json()["detail"] == "Invalid token"


@pytest.mark.asyncio
class TestGetCurrentUser:
    """Test class for get_current_user function."""

    async def test_get_current_user_valid_token(self, test_token):
        """Test get_current_user with a valid token."""
        user = await get_current_user(
            authorization=f"Bearer {test_token}", verify_token=True
        )
        assert user["sub"] == "<EMAIL>"
        assert user["active"] is True
        assert "customer" in user["roles"]
        assert user["scope"] == ["read", "write"]

    async def test_get_current_user_invalid_token(self):
        """Test get_current_user with an invalid token."""
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(
                authorization="Bearer invalid_token", verify_token=True
            )
        assert exc_info.value.status_code == 401


@pytest.mark.asyncio
class TestProtectedRoute:
    """Test class for a protected route that requires authentication."""

    async def test_protected_route_valid_token(self, test_client, test_token):
        """Test access to a protected route with a valid token."""
        response = test_client.get(
            "/inventory/items/1", headers={"Authorization": f"Bearer {test_token}"}
        )
        assert response.status_code == 200
        item = response.json()
        assert "id" in item
        assert "product_id" in item
        assert "quantity_on_hand" in item

    async def test_protected_route_invalid_token(self, test_client):
        """Test access to a protected route with an invalid token."""
        response = test_client.get(
            "/inventory/items/1", headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid token"

    async def test_protected_route_no_token(self, test_client):
        """Test access to a protected route without a token."""
        response = test_client.get("/inventory/items/1")
        assert response.status_code == 401
        assert response.json()["detail"] == "Not authenticated"
