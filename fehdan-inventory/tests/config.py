"""
Test configuration and utilities.
"""

import os
import sys

# Add the project root to Python path for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Test configuration
TEST_CONFIG = {
    "database": {
        "create_tables": True,
        "cleanup_after": False,  # Set to True to clean up test data
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    },
    "timeouts": {
        "inventory_creation": 5,  # seconds
        "database_operation": 10,  # seconds
    },
}

# Test data ranges
TEST_RANGES = {
    "product_id": (10000, 99999),
    "order_id": (1000, 9999),
    "customer_id": (1, 100),
}
