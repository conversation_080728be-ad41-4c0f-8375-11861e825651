"""
Pytest configuration and fixtures for inventory service tests.
"""

import os
import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

from app.core.database import Base
from app.events.consumer import EventConsumer
from app.services.inventory_item import InventoryService
from sqlalchemy import text

# Load test environment variables
load_dotenv(".env.test", override=True)

# Create test engine (simpler approach)
TEST_DATABASE_URL = os.getenv("DATABASE_URL")
test_engine = create_async_engine(
    TEST_DATABASE_URL, echo=False, pool_pre_ping=True, pool_recycle=300
)

TestSessionLocal = sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a fresh database session for each test."""
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # Create session
    async with TestSessionLocal() as session:
        try:
            yield session
        finally:
            # Clean up after each test
            try:
                await session.execute(text("DELETE FROM inventory_items"))
                await session.commit()
            except Exception:
                await session.rollback()


@pytest_asyncio.fixture
async def inventory_service(db_session: AsyncSession) -> InventoryService:
    """Create an inventory service instance."""
    return InventoryService(db_session)


@pytest_asyncio.fixture
async def event_consumer() -> EventConsumer:
    """Create an event consumer instance."""
    return EventConsumer()


@pytest.fixture
def sample_product_data():
    """Sample product data for testing."""
    return {
        "product_id": 12345,
        "name": "Test Beef Steak",
        "description": "Premium beef steak for testing",
        "category": "meat",
        "price": 25.99,
        "weight": 0.5,
        "unit": "kg",
        "created_by": 1,
        "status": "active",
    }


@pytest.fixture
def sample_order_data():
    """Sample order data for testing."""
    return {
        "order_id": 1001,
        "customer_id": 1,
        "status": "pending",
        "items": [
            {"product_id": 12345, "quantity": 2, "price": 25.99},
            {"product_id": 12346, "quantity": 1, "price": 15.99},
        ],
        "total_amount": 67.97,
        "created_by": 1,
        "shipping_address": "123 Test Street, Test City",
        "payment_method": "credit_card",
    }


@pytest.fixture
def sample_inventory_data():
    """Sample inventory item data for testing."""
    return {
        "product_id": 12345,
        "location_id": 1,
        "quantity_on_hand": 50,
        "reserved_quantity": 0,
        "reorder_point": 10,
        "max_stock_level": 100,
    }
