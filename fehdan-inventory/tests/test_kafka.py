#!/usr/bin/env python3
"""
Kafka test script for the inventory service.
This script demonstrates how to produce and consume messages using Kafka.
"""

import asyncio
import json
import logging
from app.events.kafka_config import kafka_config
from app.events.publisher import EventPublisher
from app.events.consumer import EventConsumer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_kafka_producer():
    """Test Kafka producer functionality."""
    logger.info("Testing Kafka Producer...")

    publisher = EventPublisher()

    # Test inventory event
    await publisher.publish_inventory_item_created(
        item_id=1, product_id=101, location_id=1, quantity=100, created_by="test_user"
    )

    # Test custom event
    test_event = {
        "event_type": "TEST_EVENT",
        "event_id": "test-123",
        "timestamp": "2025-09-22T10:00:00Z",
        "service": "inventory_service",
        "data": {"message": "Hello Kafka!", "test_data": {"key": "value"}},
    }

    await publisher.publish_event(test_event, topic="inventory")

    # Flush messages
    publisher.flush()
    logger.info("Producer test completed")


def test_kafka_consumer():
    """Test Kafka consumer functionality."""
    logger.info("Testing Kafka Consumer...")

    consumer = EventConsumer(group_id="test-consumer-group")

    # Register test handler
    def test_handler(event_data):
        logger.info(f"Received test event: {event_data}")

    consumer.register_handler("TEST_EVENT", test_handler)

    # Start consuming (this will run until stopped)
    try:
        consumer.start_consuming(topics=["inventory", "orders", "products"])
    except KeyboardInterrupt:
        logger.info("Consumer stopped by user")
    finally:
        consumer.close()


def test_config():
    """Test Kafka configuration."""
    logger.info("Testing Kafka Configuration...")

    # Validate configuration
    if kafka_config.validate_config():
        logger.info("✓ Kafka configuration is valid")

        # Display configuration (without sensitive data)
        config = kafka_config.get_producer_config()
        safe_config = {k: v for k, v in config.items() if "password" not in k.lower()}
        logger.info(f"Producer config: {safe_config}")

        # Display topics
        topics = kafka_config.topics
        logger.info(f"Available topics: {topics}")

    else:
        logger.error("✗ Kafka configuration is invalid")


async def main():
    """Main test function."""
    logger.info("🚀 Starting Kafka Integration Tests")
    logger.info("=" * 50)

    # Test 1: Configuration
    test_config()

    # Test 2: Producer
    try:
        await test_kafka_producer()
    except Exception as e:
        logger.error(f"Producer test failed: {e}")

    # Test 3: Consumer (comment out for CI/automated testing)
    # Uncomment the following lines to test consumer interactively
    """
    print("\nPress Enter to start consumer test (Ctrl+C to stop)...")
    input()
    try:
        test_kafka_consumer()
    except Exception as e:
        logger.error(f"Consumer test failed: {e}")
    """

    logger.info("🎉 Kafka integration tests completed")


if __name__ == "__main__":
    asyncio.run(main())
