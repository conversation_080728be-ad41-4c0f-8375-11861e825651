"""
Inventory Service - FastAPI Application

This is the main entry point for the Inventory Service microservice.
It handles inventory items management including stock levels, locations, and movements.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging

from app.core.config import settings
from app.core.database import create_tables
from app.core.logging import setup_logging
from app.api.v1.endpoints import inventory
from app.events.consumer import EventConsumer

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    try:
        # Create database tables
        await create_tables()
        logger.info("Database tables created/verified")

        # Initialize Kafka event consumer
        consumer = EventConsumer()
        await consumer.start()
        logger.info("Kafka event consumer started")

        yield

    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        raise
    finally:
        # Cleanup
        try:
            await consumer.stop()
            logger.info("Application cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Create FastAPI application
app = FastAPI(
    title="Inventory Service",
    description="Microservice for managing inventory items, stock levels, and locations",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(status_code=500, content={"detail": "Internal server error"})


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        return {
            "status": "healthy",
            "service": "inventory-service",
            "version": "1.0.0",
            "database": "connected",
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "inventory-service",
                "version": "1.0.0",
                "error": str(e),
            },
        )


# Include API routes
app.include_router(
    inventory.router, prefix=f"{settings.API_V1_STR}/inventory", tags=["inventory"]
)


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "Inventory Service",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
