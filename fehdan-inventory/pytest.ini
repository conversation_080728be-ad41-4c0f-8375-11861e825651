[tool.pytest.ini_options]
minversion = "6.0"
addopts = -ra --strict-markers --strict-config -v
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    events: marks tests as event tests
    services: marks tests as service tests
asyncio_mode = auto
log_cli = true
log_cli_level = INFO