#!/usr/bin/env python3
"""
Test script to verify JWT implementation and hybrid introspection approach.
"""

import asyncio
import httpx
from app.services.auth import get_current_user
from app.core.config import settings
from fastapi import Header


# Test token provided by user
TEST_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************.TKka03YGSZg2GCOJAKfrCKWaoGy30Parx80aRV9_rGaC2kdfEuPB0O5fBmFYPUCC1ue3buu2KkZQ9B_xD_4AUBS35j6YjwzuQtrrLg6Gsk-HywbtLrSc_fVbD9QBJ9PqiJJ6nvL2H02OvsGOeIBfZHCGwa76YZm-ijKHndb5s1CYPzLyQIO033ghp1jhNH7zOtgF5KKe9Xjj-i6mTZDqh754WPOj0HMxzCtzSLXwG7FZ3S9N3o1By3LL3fMI56ku8aeTf3BTUOIMEwGvuSLVVCjuCwbN5FyNoAKHRTaLtqTuyj4MvInVcbeifvP3tt4YcxZeOSAnie9tZyr_RIXfNg"


async def test_jwt_verification():
    """Test JWT verification functionality."""

    print("🧪 Testing JWT Implementation")
    print("=" * 50)

    # 1. Check configuration
    print(f"✓ Auth Service URL: {settings.AUTH_SERVICE_URL}")
    print(f"✓ JWT Algorithm: {settings.JWT_ALGORITHM}")
    print(f"✓ JWT Secret Key Present: {'Yes' if settings.JWT_SECRET_KEY else 'No'}")
    print(f"✓ Public Key Present: {'Yes' if settings.PUBLIC_KEY else 'No'}")
    print()

    # 2. Test getting JWKS from auth service
    print("🔑 Testing JWKS retrieval from auth service...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{settings.AUTH_SERVICE_URL}/auth/.well-known/jwks.json"
            )
            if response.status_code == 200:
                jwks = response.json()
                print(f"✓ JWKS retrieved successfully")
                print(f"  Keys found: {len(jwks.get('keys', []))}")
                if jwks.get("keys"):
                    first_key = jwks["keys"][0]
                    print(f"  First key type: {first_key.get('kty')}")
                    print(f"  Algorithm: {first_key.get('alg')}")
                    # Extract public key for local verification
                    if first_key.get("kty") == "RSA":
                        n = first_key.get("n")
                        e = first_key.get("e")
                        print(f"  ✓ RSA public key components found")
            else:
                print(f"✗ Failed to retrieve JWKS: {response.status_code}")
                print(f"  Response: {response.text[:200]}")
    except Exception as e:
        print(f"✗ Error retrieving JWKS: {e}")
    print()

    # 3. Test token introspection endpoint
    print("🔍 Testing token introspection endpoint...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{settings.AUTH_SERVICE_URL}/auth/verify-token",
                json={"token": TEST_TOKEN},
                timeout=10.0,
            )
            print(f"✓ Introspection endpoint status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"✓ Token verified successfully!")
                print(f"  User: {result.get('sub', 'N/A')}")
                print(f"  Active: {result.get('active', 'N/A')}")
                print(f"  Roles: {result.get('roles', [])}")
                print(f"  Scope: {result.get('scope', [])}")
            else:
                print(f"✗ Token verification failed: {response.status_code}")
                print(f"  Response: {response.text[:200]}")
    except Exception as e:
        print(f"✗ Error testing introspection: {e}")
    print()

    # 4. Test local JWT verification with the provided token
    print("🔐 Testing local JWT verification with provided token...")
    try:
        authorization = f"Bearer {TEST_TOKEN}"
        result = await get_current_user(authorization=authorization, verify_token=False)
        print(f"✓ Local JWT verification successful!")
        print(f"  User: {result.get('sub', 'N/A')}")
        print(f"  Active: {result.get('active', 'N/A')}")
        print(f"  Roles: {result.get('roles', [])}")
        print(f"  Scope: {result.get('scope', [])}")
    except Exception as e:
        print(f"✗ Local JWT verification failed: {e}")
    print()

    # 5. Test hybrid introspection (verify_token=True)
    print("🔄 Testing hybrid introspection (verify_token=True)...")
    try:
        authorization = f"Bearer {TEST_TOKEN}"
        result = await get_current_user(authorization=authorization, verify_token=True)
        print(f"✓ Hybrid introspection successful!")
        print(f"  User: {result.get('sub', 'N/A')}")
        print(f"  Active: {result.get('active', 'N/A')}")
        print(f"  Roles: {result.get('roles', [])}")
        print(f"  Scope: {result.get('scope', [])}")
    except Exception as e:
        print(f"✗ Hybrid introspection failed: {e}")
    print()


async def test_hybrid_introspection_approach():
    """Test the hybrid introspection approach for critical endpoints."""

    print("\n🔄 Testing Hybrid Introspection Approach")
    print("=" * 50)

    print("Current Implementation Analysis:")
    print("✓ Local JWT verification implemented for fast validation")
    print("✓ Token introspection available for critical endpoints")
    print("✓ Scope-based authorization implemented (read:inventory)")

    # Check if we need to modify the critical endpoint
    print("\nRecommendations for Critical Endpoints:")
    print("1. Update endpoint should use verify_token=True")
    print("2. Delete endpoint should use verify_token=True")
    print("3. Create endpoint could use local verification (less critical)")
    print("4. Read endpoints can use local verification (fastest)")


if __name__ == "__main__":
    asyncio.run(test_jwt_verification())
    asyncio.run(test_hybrid_introspection_approach())
