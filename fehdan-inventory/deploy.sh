#!/bin/bash

# Docker deployment script for Inventory Service
set -e

echo "🚀 <PERSON>hdan Meat Processing - Inventory Service Deployment"
echo "======================================================="

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✓ $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m✗ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ $1\033[0m"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    print_info "Please copy .env.example to .env and configure your environment variables"
    exit 1
fi

print_status ".env file found"

# Load environment variables
set -o allexport
source .env
set +o allexport

# Validate required environment variables
required_vars=(
    "DATABASE_URL"
    "JWT_SECRET_KEY"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Required environment variable $var is not set"
        exit 1
    fi
done

print_status "Environment variables validated"

# Parse command line arguments
ACTION=${1:-"up"}

case $ACTION in
    "build")
        print_info "Building Docker image..."
        docker-compose build --no-cache
        print_status "Docker image built successfully"
        ;;
    
    "up")
        print_info "Starting Inventory Service..."
        docker-compose up -d
        print_status "Inventory Service started"
        
        print_info "Waiting for service to be healthy..."
        timeout 60 bash -c 'until curl -f http://localhost:8000/health >/dev/null 2>&1; do sleep 2; done'
        print_status "Service is healthy and ready!"
        
        echo ""
        print_info "Service URLs:"
        echo "  📊 API Documentation: http://localhost:8000/docs"
        echo "  🏥 Health Check: http://localhost:8000/health"
        echo "  📋 Service Info: http://localhost:8000/"
        ;;
    
    "down")
        print_info "Stopping Inventory Service..."
        docker-compose down
        print_status "Service stopped"
        ;;
    
    "restart")
        print_info "Restarting Inventory Service..."
        docker-compose restart
        print_status "Service restarted"
        ;;
    
    "logs")
        print_info "Showing service logs..."
        docker-compose logs -f inventory-service
        ;;
    
    "shell")
        print_info "Opening shell in running container..."
        docker-compose exec inventory-service bash
        ;;
    
    "clean")
        print_info "Cleaning up Docker resources..."
        docker-compose down -v
        docker system prune -f
        print_status "Cleanup completed"
        ;;
    
    *)
        echo "Usage: $0 {build|up|down|restart|logs|shell|clean}"
        echo ""
        echo "Commands:"
        echo "  build    - Build the Docker image"
        echo "  up       - Start the service (default)"
        echo "  down     - Stop the service"
        echo "  restart  - Restart the service"
        echo "  logs     - Show service logs"
        echo "  shell    - Open shell in container"
        echo "  clean    - Clean up Docker resources"
        exit 1
        ;;
esac