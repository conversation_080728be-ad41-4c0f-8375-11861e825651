services:
  inventory-service:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      # Override any specific environment variables if needed
      - ENVIRONMENT=production
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy
    restart: unless-stopped
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

networks:
  default:
    name: fehdan-inventory-network