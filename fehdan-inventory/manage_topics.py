#!/usr/bin/env python3
"""
Kafka topic management script for the inventory service.
This script helps create and manage Kafka topics in your cluster.
"""

import logging
from confluent_kafka.admin import AdminClient, NewTopic, ConfigResource, ResourceType
from app.events.kafka_config import kafka_config

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_topics():
    """Create necessary Kafka topics for the inventory service."""
    logger.info("Creating Kafka topics...")

    # Get admin client
    admin_config = kafka_config.get_admin_config()
    admin_client = AdminClient(admin_config)

    # Define topics to create
    topics_to_create = [
        NewTopic(
            topic="inventory",
            num_partitions=3,
            replication_factor=3,
            config={
                "cleanup.policy": "compact,delete",
                "retention.ms": str(7 * 24 * 60 * 60 * 1000),  # 7 days
                "compression.type": "snappy",
            },
        ),
        NewTopic(
            topic="orders",
            num_partitions=3,
            replication_factor=3,
            config={
                "cleanup.policy": "compact,delete",
                "retention.ms": str(7 * 24 * 60 * 60 * 1000),  # 7 days
                "compression.type": "snappy",
            },
        ),
        NewTopic(
            topic="products",
            num_partitions=3,
            replication_factor=3,
            config={
                "cleanup.policy": "compact,delete",
                "retention.ms": str(7 * 24 * 60 * 60 * 1000),  # 7 days
                "compression.type": "snappy",
            },
        ),
        NewTopic(
            topic="auth",
            num_partitions=3,
            replication_factor=3,
            config={
                "cleanup.policy": "compact,delete",
                "retention.ms": str(7 * 24 * 60 * 60 * 1000),  # 7 days
                "compression.type": "snappy",
            },
        ),
        NewTopic(
            topic="notifications",
            num_partitions=3,
            replication_factor=3,
            config={
                "cleanup.policy": "delete",
                "retention.ms": str(24 * 60 * 60 * 1000),  # 1 day
                "compression.type": "snappy",
            },
        ),
    ]

    # Create topics
    topic_futures = admin_client.create_topics(topics_to_create, validate_only=False)

    # Wait for results
    for topic_name, future in topic_futures.items():
        try:
            future.result()  # The result itself is None
            logger.info(f"✓ Topic '{topic_name}' created successfully")
        except Exception as e:
            if "already exists" in str(e).lower():
                logger.info(f"ℹ Topic '{topic_name}' already exists")
            else:
                logger.error(f"✗ Failed to create topic '{topic_name}': {e}")


def list_topics():
    """List all available topics in the cluster."""
    logger.info("Listing Kafka topics...")

    admin_config = kafka_config.get_admin_config()
    admin_client = AdminClient(admin_config)

    metadata = admin_client.list_topics(timeout=10)

    logger.info(f"Available topics ({len(metadata.topics)}):")
    for topic_name in sorted(metadata.topics.keys()):
        topic = metadata.topics[topic_name]
        logger.info(f"  - {topic_name} (partitions: {len(topic.partitions)})")


def describe_topics(topic_names=None):
    """Describe specific topics or all topics."""
    if topic_names is None:
        topic_names = ["inventory", "orders", "products", "auth", "notifications"]

    logger.info(f"Describing topics: {topic_names}")

    admin_config = kafka_config.get_admin_config()
    admin_client = AdminClient(admin_config)

    metadata = admin_client.list_topics(timeout=10)

    for topic_name in topic_names:
        if topic_name in metadata.topics:
            topic = metadata.topics[topic_name]
            logger.info(f"\nTopic: {topic_name}")
            logger.info(f"  Partitions: {len(topic.partitions)}")
            logger.info(f"  Error: {topic.error}")

            for partition_id, partition in topic.partitions.items():
                logger.info(f"  Partition {partition_id}:")
                logger.info(f"    Leader: {partition.leader}")
                logger.info(f"    Replicas: {partition.replicas}")
                logger.info(f"    ISR: {partition.isrs}")
        else:
            logger.warning(f"  Topic '{topic_name}' not found")


def main():
    """Main function with menu options."""
    logger.info("🚀 Kafka Topic Management")
    logger.info("=" * 50)

    # Validate configuration first
    if not kafka_config.validate_config():
        logger.error("❌ Kafka configuration is invalid. Please check your .env file.")
        return

    logger.info("✅ Kafka configuration is valid")

    while True:
        print("\nKafka Topic Management Options:")
        print("1. Create topics")
        print("2. List all topics")
        print("3. Describe inventory service topics")
        print("4. Exit")

        choice = input("\nEnter your choice (1-4): ").strip()

        if choice == "1":
            try:
                create_topics()
            except Exception as e:
                logger.error(f"Failed to create topics: {e}")

        elif choice == "2":
            try:
                list_topics()
            except Exception as e:
                logger.error(f"Failed to list topics: {e}")

        elif choice == "3":
            try:
                describe_topics()
            except Exception as e:
                logger.error(f"Failed to describe topics: {e}")

        elif choice == "4":
            logger.info("👋 Goodbye!")
            break

        else:
            print("Invalid choice. Please enter 1-4.")


if __name__ == "__main__":
    main()
