from sqlalchemy import (
    Column,
    Integer,
    String,
    Boolean,
    DateTime,
    Text,
    DECIMAL,
    Enum,
)
import enum
import datetime

from app.core.database import Base


class LocationType(str, enum.Enum):
    WAREHOUSE = "warehouse"
    FREEZER = "freezer"
    COOLER = "cooler"
    DISPLAY = "display"
    PROCESSING = "processing"


class InventoryItem(Base):
    __tablename__ = "inventory_items"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(
        Integer, nullable=False, index=True
    )  # Reference to product service
    location_id = Column(Integer, nullable=False)  # Reference to location

    # Stock information
    quantity_on_hand = Column(Integer, default=0)
    reserved_quantity = Column(Integer, default=0)  # For pending orders
    available_quantity = Column(Integer, default=0)  # on_hand - reserved
    reorder_point = Column(Integer, default=10)
    max_stock_level = Column(Integer)

    # Tracking information
    batch_number = Column(String(100))
    lot_number = Column(String(100))
    received_date = Column(DateTime)
    expiration_date = Column(DateTime)

    # Status
    is_active = Column(Boolean, default=True)

    # User tracking (references to auth service users)
    created_by = Column(Integer)
    updated_by = Column(Integer)

    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow
    )


class Location(Base):
    __tablename__ = "locations"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    code = Column(
        String(20), nullable=False, unique=True
    )  # Short code like "WH-01", "FZ-A1"
    location_type = Column(Enum(LocationType), nullable=False)

    # Physical details
    temperature_min = Column(DECIMAL(5, 2))  # Minimum temperature in Fahrenheit
    temperature_max = Column(DECIMAL(5, 2))  # Maximum temperature in Fahrenheit
    capacity = Column(String(100))  # Storage capacity description

    # Address/Description
    description = Column(Text)
    address = Column(Text)

    # Status
    is_active = Column(Boolean, default=True)
    is_temperature_controlled = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow
    )


class InventoryAlert(Base):
    __tablename__ = "inventory_alerts"

    id = Column(Integer, primary_key=True, index=True)
    inventory_item_id = Column(Integer, nullable=False)  # Reference to inventory_items

    # Alert details
    alert_type = Column(String(50), nullable=False)  # low_stock, expiring_soon, expired
    message = Column(String(255), nullable=False)
    priority = Column(String(20), default="medium")  # low, medium, high, critical

    # Status
    is_resolved = Column(Boolean, default=False)
    resolved_by = Column(Integer)  # Reference to auth service users
    resolved_at = Column(DateTime)

    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)

    # Additional data
    threshold_value = Column(Integer)  # Stock level that triggered the alert
    current_value = Column(Integer)  # Current stock level when alert was created
