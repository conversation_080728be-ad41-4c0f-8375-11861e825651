from pydantic import BaseModel, Field, field_validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from .models import LocationType


class LocationCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    code: str = Field(..., min_length=1, max_length=20)
    location_type: LocationType
    temperature_min: Optional[Decimal] = Field(None, ge=-100, le=100)
    temperature_max: Optional[Decimal] = Field(None, ge=-100, le=100)
    capacity: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    address: Optional[str] = None
    is_temperature_controlled: bool = False

    @field_validator("temperature_max")
    @classmethod
    def validate_temperature_range(cls, v, info):
        if (
            v is not None
            and "temperature_min" in info.data
            and info.data["temperature_min"] is not None
        ):
            if v <= info.data["temperature_min"]:
                raise ValueError("temperature_max must be greater than temperature_min")
        return v


class LocationResponse(BaseModel):
    id: int
    name: str
    code: str
    location_type: LocationType
    temperature_min: Optional[Decimal]
    temperature_max: Optional[Decimal]
    capacity: Optional[str]
    description: Optional[str]
    address: Optional[str]
    is_active: bool
    is_temperature_controlled: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class InventoryItemCreate(BaseModel):
    product_id: int = Field(..., gt=0)
    location_id: int = Field(..., gt=0)
    quantity_on_hand: int = Field(default=0, ge=0)
    reserved_quantity: int = Field(default=0, ge=0)
    reorder_point: int = Field(default=10, ge=0)
    max_stock_level: Optional[int] = Field(None, gt=0)
    batch_number: Optional[str] = Field(None, max_length=100)
    lot_number: Optional[str] = Field(None, max_length=100)
    received_date: Optional[datetime] = None
    expiration_date: Optional[datetime] = None

    @field_validator("max_stock_level")
    @classmethod
    def validate_max_stock_level(cls, v, info):
        if v is not None and "reorder_point" in info.data:
            if v <= info.data["reorder_point"]:
                raise ValueError("max_stock_level must be greater than reorder_point")
        return v

    @field_validator("expiration_date")
    @classmethod
    def validate_expiration_date(cls, v, info):
        if (
            v is not None
            and "received_date" in info.data
            and info.data["received_date"] is not None
        ):
            if v <= info.data["received_date"]:
                raise ValueError("expiration_date must be after received_date")
        return v

    @field_validator("reserved_quantity")
    @classmethod
    def validate_reserved_quantity(cls, v, info):
        if "quantity_on_hand" in info.data and v > info.data["quantity_on_hand"]:
            raise ValueError("reserved_quantity cannot exceed quantity_on_hand")
        return v


class InventoryItemUpdate(BaseModel):
    quantity_on_hand: Optional[int] = Field(None, ge=0)
    reserved_quantity: Optional[int] = Field(None, ge=0)
    reorder_point: Optional[int] = Field(None, ge=0)
    max_stock_level: Optional[int] = Field(None, gt=0)
    batch_number: Optional[str] = Field(None, max_length=100)
    lot_number: Optional[str] = Field(None, max_length=100)
    received_date: Optional[datetime] = None
    expiration_date: Optional[datetime] = None
    is_active: Optional[bool] = None


class InventoryItemResponse(BaseModel):
    id: int
    product_id: int
    location_id: int
    quantity_on_hand: int
    reserved_quantity: int
    available_quantity: int
    reorder_point: int
    max_stock_level: Optional[int]
    batch_number: Optional[str]
    lot_number: Optional[str]
    received_date: Optional[datetime]
    expiration_date: Optional[datetime]
    is_active: bool
    created_by: Optional[int]
    updated_by: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class LowStockAlert(BaseModel):
    inventory_item_id: int
    product_id: int
    location_name: str
    current_quantity: int
    reorder_point: int
    message: str


class ExpirationAlert(BaseModel):
    inventory_item_id: int
    product_id: int
    location_name: str
    batch_number: Optional[str]
    expiration_date: datetime
    days_until_expiration: int
    message: str


class CurrentUserResponse(BaseModel):
    active: bool
    sub: str
    roles: list[str]
    scope: list[str]
