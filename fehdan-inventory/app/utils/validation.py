from typing import Optional
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import LoggingService
from app.repositories.inventory import InventoryRepository
from app.schemas import InventoryItemCreate


class ValidationUtils:
    def __init__(self, db: AsyncSession = None):
        self.db = db
        if db:
            self.inventory_repo = InventoryRepository(db)
        self.logger = LoggingService()

    async def validate_stock_item_creation(
        self, item_data: InventoryItemCreate
    ) -> None:
        """Validate business rules for stock item creation."""

        # Check if product exists
        product_exists = await self.validate_product_exists(item_data.product_id)
        if not product_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Product with ID {item_data.product_id} not found",
            )

        # Check if location exists and is active
        location_exists = await self.validate_location_exists(item_data.location_id)
        if not location_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Location with ID {item_data.location_id} not found",
            )

        # Check for duplicate inventory item (same product + location)
        existing_item = await self.check_existing_inventory_item(
            item_data.product_id, item_data.location_id
        )
        if existing_item:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Inventory item already exists for product {item_data.product_id} at location {item_data.location_id}",
            )

        # Validate temperature requirements for perishable items
        if item_data.expiration_date:
            await self.validate_temperature_requirements(item_data.location_id)

    async def check_existing_inventory_item(
        self, product_id: int, location_id: int
    ) -> bool:
        """Check if inventory item already exists for product at location."""
        try:
            if not self.inventory_repo:
                return False

            result = (
                await self.inventory_repo.get_inventory_item_by_product_and_location(
                    product_id, location_id
                )
            )
            return result
        except Exception as e:
            self.logger.log(
                f"Error checking existing inventory item: {str(e)}",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return False

    async def validate_temperature_requirements(self, location_id: int) -> None:
        """Validate that location has appropriate temperature controls for perishable items."""
        self.logger.log(
            f"TODO: Validate temperature requirements for location {location_id}",
            level="warning",
            app_name="inventory",
        )

    async def validate_product_exists(self, product_id: int) -> bool:
        """Check if product exists."""

    async def validate_location_exists(self, location_id: int) -> bool:
        """Check if location exists and is active."""
        try:
            if not self.inventory_repo:
                return False

            result = await self.inventory_repo.get_location_by_id(location_id)
            return result
        except Exception as e:
            self.logger.log(
                f"Error validating location {location_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="inventory",
            )
            return False
