from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, DeclarativeBase
from contextlib import asynccontextmanager
from app.core.config import settings


# Create async engine with properly formatted URL
engine = create_async_engine(settings.async_database_url, echo=True)


# Create async session factory
async_session = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


# Base class for declarative models
class Base(DeclarativeBase):
    pass


# Function to create tables
async def create_tables():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


# Dependency to get DB session
async def get_db():
    async with async_session() as session:
        yield session


@asynccontextmanager
async def get_db_session():
    """
    Create a database session for use outside of FastAPI dependency injection.
    Useful for management scripts and background tasks.
    """
    async with async_session() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
