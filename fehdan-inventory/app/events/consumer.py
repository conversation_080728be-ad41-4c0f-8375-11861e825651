"""
Event consumer for consuming events from Kafka topics.
"""

import asyncio
import json
import logging
from typing import Dict, Callable, List
from confluent_kafka import Consumer, KafkaError
from app.events.kafka_config import kafka_config
from app.events.schemas import (
    OrderEventType,
    ProductEventType,
    OrderCreatedEvent,
    OrderCancelledEvent,
    ProductCreatedEvent,
    ProductUpdatedEvent,
    ProductDeletedEvent,
)

logger = logging.getLogger(__name__)


class EventConsumer:
    """Consumes events from Kafka topics."""

    def __init__(self, group_id: str = "inventory-service-group"):
        self.group_id = group_id
        self.consumer = None
        self.orders_topic = kafka_config.get_topic_name("orders")
        self.products_topic = kafka_config.get_topic_name("products")
        self.event_handlers: Dict[str, Callable] = {}
        self.is_running = False
        self._consume_task = None

    def _get_consumer(self) -> Consumer:
        """Get or create Kafka consumer instance."""
        if self.consumer is None:
            consumer_config = kafka_config.get_consumer_config(self.group_id)
            self.consumer = Consumer(consumer_config)
            logger.info(f"Kafka consumer initialized with group_id: {self.group_id}")
        return self.consumer

    def register_handler(self, event_type: str, handler: Callable):
        """Register an event handler for a specific event type."""
        self.event_handlers[event_type] = handler
        logger.info(f"Registered handler for event type: {event_type}")

    async def handle_order_created(self, event_data: dict):
        """Handle ORDER_CREATED event."""
        try:
            event = OrderCreatedEvent(**event_data)
            logger.info(f"Processing order created event: {event.event_id}")

            # Business logic for handling order creation
            # Example: Reserve stock for order items
            order_data = event.data
            order_id = order_data.get("order_id")
            items = order_data.get("items", [])

            for item in items:
                product_id = item.get("product_id")
                quantity = item.get("quantity")
                location_id = item.get("location_id", 1)  # Default location

                # TODO: Implement stock reservation logic
                logger.info(
                    f"Need to reserve {quantity} units of product {product_id} for order {order_id}"
                )

        except Exception as e:
            logger.error(f"Error handling ORDER_CREATED event: {e}")

    async def handle_order_cancelled(self, event_data: dict):
        """Handle ORDER_CANCELLED event."""
        try:
            event = OrderCancelledEvent(**event_data)
            logger.info(f"Processing order cancelled event: {event.event_id}")

            # Business logic for handling order cancellation
            # Example: Release reserved stock
            order_data = event.data
            order_id = order_data.get("order_id")

            # TODO: Implement stock release logic
            logger.info(
                f"Need to release reserved stock for cancelled order {order_id}"
            )

        except Exception as e:
            logger.error(f"Error handling ORDER_CANCELLED event: {e}")

    async def handle_product_created(self, event_data: dict):
        """Handle PRODUCT_CREATED event by creating an inventory item."""
        try:
            event = ProductCreatedEvent(**event_data)
            logger.info(f"Processing product created event: {event.event_id}")

            # Extract product data
            product_data = event.data
            product_id = product_data.get("product_id")
            product_name = product_data.get("name", f"Product {product_id}")

            logger.info(
                f"Creating inventory item for new product: {product_name} (ID: {product_id})"
            )

            # Import here to avoid circular imports
            from app.core.database import get_db
            from app.services.inventory_item import InventoryService, User
            from app.schemas import InventoryItemCreate
            from app.events.publisher import event_publisher

            try:
                # Get database connection
                db = await get_db().__anext__()
                inventory_service = InventoryService(db)

                # Create inventory item data
                inventory_data = InventoryItemCreate(
                    product_id=product_id,
                    location_id=1,  # Default location
                    quantity_on_hand=0,  # Start with zero stock
                    reorder_point=10,  # Default reorder point
                    max_stock_level=1000,  # Default max stock
                )

                # Create system user for automated operations
                system_user = User(email="<EMAIL>", id=0)

                # Create the inventory item
                inventory_item = await inventory_service.create_stock_item(
                    item_data=inventory_data,
                    current_user=system_user,
                    skip_product_validation=True,  # Skip validation since product might not exist in this service
                    skip_location_validation=True,
                )

                logger.info(
                    f"✅ Successfully created inventory item {inventory_item.id} for product {product_id}"
                )

                # Publish inventory item created event
                await event_publisher.publish_inventory_item_created(
                    item_id=inventory_item.id,
                    product_id=product_id,
                    location_id=1,
                    quantity=0,
                    created_by="<EMAIL>",
                )

                logger.info(
                    f"📤 Published INVENTORY_ITEM_CREATED event for item {inventory_item.id}"
                )

            except Exception as creation_error:
                logger.error(
                    f"❌ Failed to create inventory item for product {product_id}: {creation_error}"
                )
                # Log but don't re-raise to prevent consumer from stopping

        except Exception as e:
            logger.error(f"Error handling PRODUCT_CREATED event: {e}")

    async def handle_product_updated(self, event_data: dict):
        """Handle PRODUCT_UPDATED event."""
        try:
            event = ProductUpdatedEvent(**event_data)
            logger.info(f"Processing product updated event: {event.event_id}")

            # Business logic for handling product updates
            product_data = event.data
            product_id = product_data.get("product_id")

            # TODO: Implement product inventory updates
            logger.info(f"Need to update inventory for product {product_id}")

        except Exception as e:
            logger.error(f"Error handling PRODUCT_UPDATED event: {e}")

    async def handle_product_deleted(self, event_data: dict):
        """Handle PRODUCT_DELETED event."""
        try:
            event = ProductDeletedEvent(**event_data)
            logger.info(f"Processing product deleted event: {event.event_id}")

            # Business logic for handling product deletion
            product_data = event.data
            product_id = product_data.get("product_id")

            # TODO: Implement product inventory cleanup
            logger.info(f"Need to clean up inventory for deleted product {product_id}")

        except Exception as e:
            logger.error(f"Error handling PRODUCT_DELETED event: {e}")

    def process_message(self, message):
        """Process a single Kafka message."""
        try:
            # Parse the message
            event_data = json.loads(message.value().decode("utf-8"))
            event_type = event_data.get("event_type")

            if not event_type:
                logger.warning("Received message without event_type")
                return

            # Find and execute handler
            handler = self.event_handlers.get(event_type)
            if handler:
                # Check if handler is async and run appropriately
                if asyncio.iscoroutinefunction(handler):
                    # Run async handler in new event loop if none exists
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If loop is running, create task
                            asyncio.create_task(handler(event_data))
                        else:
                            # If no loop is running, run directly
                            loop.run_until_complete(handler(event_data))
                    except RuntimeError:
                        # No event loop exists, create and run
                        asyncio.run(handler(event_data))
                else:
                    # Synchronous handler
                    handler(event_data)
            else:
                logger.debug(f"No handler registered for event type: {event_type}")

        except Exception as e:
            logger.error(f"Error processing message: {e}")

    def start_consuming(self, topics: List[str] = None):
        """Start consuming events from Kafka topics."""
        if self.is_running:
            logger.warning("Event consumer is already running")
            return

        topics = topics or [self.orders_topic, self.products_topic]

        try:
            self.is_running = True
            logger.info(f"Starting event consumer for topics: {topics}")

            # Register default handlers
            self.register_handler(
                OrderEventType.ORDER_CREATED,
                self.handle_order_created,
            )
            self.register_handler(
                OrderEventType.ORDER_CANCELLED,
                self.handle_order_cancelled,
            )
            self.register_handler(
                ProductEventType.PRODUCT_CREATED,
                self.handle_product_created,
            )
            self.register_handler(
                ProductEventType.PRODUCT_UPDATED,
                self.handle_product_updated,
            )
            self.register_handler(
                ProductEventType.PRODUCT_DELETED,
                self.handle_product_deleted,
            )

            # Get consumer and subscribe to topics
            consumer = self._get_consumer()
            consumer.subscribe(topics)

            logger.info(f"Subscribed to topics: {topics}")

            # Start consuming messages
            while self.is_running:
                try:
                    message = consumer.poll(timeout=1.0)

                    if message is None:
                        continue

                    if message.error():
                        if message.error().code() == KafkaError._PARTITION_EOF:
                            # End of partition event
                            logger.debug(
                                f"Reached end of partition {message.partition()}"
                            )
                        else:
                            logger.error(f"Consumer error: {message.error()}")
                        continue

                    # Process the message
                    self.process_message(message)

                    # Commit the message
                    consumer.commit(message)

                except Exception as e:
                    logger.error(f"Error in consumer loop: {e}")
                    asyncio.sleep(1)  # Brief pause before retrying

            logger.info("Event consumer stopped")

        except Exception as e:
            logger.error(f"Fatal error in event consumer: {e}")
            self.is_running = False
        finally:
            if self.consumer:
                self.consumer.close()

    def stop_consuming(self):
        """Stop consuming events."""
        logger.info("Stopping event consumer...")
        self.is_running = False

    async def start(self):
        """Start the event consumer as a background task."""
        if self._consume_task is None or self._consume_task.done():
            self._consume_task = asyncio.create_task(
                asyncio.to_thread(self.start_consuming)
            )
        return self._consume_task

    async def stop(self):
        """Stop the event consumer."""
        self.stop_consuming()
        if self._consume_task:
            await self._consume_task

    def close(self):
        """Close the consumer and clean up resources."""
        self.stop_consuming()
        if self.consumer:
            self.consumer.close()
            self.consumer = None


# Global event consumer instance
event_consumer = None
