"""Kafka configuration for the inventory service."""

import os
from typing import Dict, Any


class KafkaConfig:
    """Minimal Ka<PERSON> configuration manager."""

    def __init__(self):
        # Base configuration from environment using standard uppercase variables
        self.config = {}

        # Read Kafka configuration from standard environment variables
        kafka_vars = {
            "bootstrap.servers": os.getenv("KAFKA_BOOTSTRAP_SERVERS"),
            "security.protocol": os.getenv("KAFKA_SECURITY_PROTOCOL"),
            "sasl.mechanisms": os.getenv("KAFKA_SASL_MECHANISMS"),
            "sasl.username": os.getenv("KAFKA_SASL_USERNAME"),
            "sasl.password": os.getenv("KAFKA_SASL_PASSWORD"),
            "session.timeout.ms": os.getenv("KAFKA_SESSION_TIMEOUT_MS", "10000"),
            "client.id": os.getenv("KAFKA_CLIENT_ID"),
        }

        # Filter out None values and convert session.timeout.ms to int
        for k, v in kafka_vars.items():
            if v is not None:
                if k == "session.timeout.ms":
                    self.config[k] = int(v)
                else:
                    self.config[k] = v

        # Validate required configs
        if not self.config.get("bootstrap.servers"):
            print(
                f"DEBUG: Available environment variables with 'kafka' or 'bootstrap': {[k for k in os.environ.keys() if 'kafka' in k.lower() or 'bootstrap' in k.lower()]}"
            )
            raise ValueError("Missing required Kafka configuration: bootstrap.servers")

        # Topic configuration
        self.topics = {
            "inventory": os.getenv("KAFKA_INVENTORY_TOPIC", "inventory"),
            "orders": os.getenv("KAFKA_ORDERS_TOPIC", "orders"),
            "products": os.getenv("KAFKA_PRODUCTS_TOPIC", "products"),
            "auth": os.getenv("KAFKA_AUTH_TOPIC", "auth"),
            "notifications": os.getenv("KAFKA_NOTIFICATIONS_TOPIC", "notifications"),
        }

    def get_producer_config(self) -> Dict[str, Any]:
        """Get producer configuration."""
        config = {k: v for k, v in self.config.items() if k != "session.timeout.ms"}
        config.update(
            {
                "acks": "all",
                "retries": 3,
                "enable.idempotence": True,
                "compression.type": "snappy",
            }
        )
        return config

    def get_consumer_config(
        self, group_id: str, auto_offset_reset: str = "earliest"
    ) -> Dict[str, Any]:
        """Get consumer configuration."""
        config = self.config.copy()
        config.update(
            {
                "group.id": group_id,
                "auto.offset.reset": auto_offset_reset,
                "enable.auto.commit": True,
            }
        )
        return config

    def get_admin_config(self) -> Dict[str, Any]:
        """Get admin configuration."""
        return self.config.copy()

    def get_topic_name(self, topic_type: str) -> str:
        """Get topic name for event type."""
        return self.topics.get(topic_type, topic_type)


# Global instance
kafka_config = KafkaConfig()
