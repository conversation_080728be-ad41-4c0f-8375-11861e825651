"""
Event schemas and types for the inventory service event system.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Any, Dict
from pydantic import BaseModel, Field
import uuid


class InventoryEventType(str, Enum):
    """Inventory channel event types."""

    INVENTORY_ITEM_CREATED = "INVENTORY_ITEM_CREATED"
    INVENTORY_ITEM_UPDATED = "INVENTORY_ITEM_UPDATED"
    INVENTORY_ITEM_DELETED = "INVENTORY_ITEM_DELETED"
    STOCK_LEVEL_CHANGED = "STOCK_LEVEL_CHANGED"
    LOW_STOCK_ALERT = "LOW_STOCK_ALERT"
    STOCK_MOVEMENT_RECORDED = "STOCK_MOVEMENT_RECORDED"
    REORDER_POINT_REACHED = "REORDER_POINT_REACHED"


class OrderEventType(str, Enum):
    """Order channel event types (consumed)."""

    ORDER_CREATED = "ORDER_CREATED"
    ORDER_CANCELLED = "ORDER_CANCELLED"
    ORDER_FULFILLED = "ORDER_FULFILLED"
    STOCK_RESERVED = "STOCK_RESERVED"
    STOCK_RELEASED = "STOCK_RELEASED"


class ProductEventType(str, Enum):
    """Product channel event types (consumed)."""

    PRODUCT_CREATED = "PRODUCT_CREATED"
    PRODUCT_UPDATED = "PRODUCT_UPDATED"
    PRODUCT_DELETED = "PRODUCT_DELETED"


class BaseEvent(BaseModel):
    """Base event schema."""

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="inventory_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]


class InventoryItemCreatedEvent(BaseModel):
    """Event published when an inventory item is created."""

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=InventoryEventType.INVENTORY_ITEM_CREATED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="inventory_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]

    @classmethod
    def create(
        cls,
        item_id: int,
        product_id: int,
        location_id: int,
        quantity: int,
        created_by: str,
    ):
        return cls(
            data={
                "item_id": item_id,
                "product_id": product_id,
                "location_id": location_id,
                "quantity_on_hand": quantity,
                "created_by": created_by,
            }
        )


class InventoryItemUpdatedEvent(BaseModel):
    """Event published when an inventory item is updated."""

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=InventoryEventType.INVENTORY_ITEM_UPDATED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="inventory_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]

    @classmethod
    def create(
        cls,
        item_id: int,
        product_id: int,
        location_id: int,
        updated_fields: Dict[str, Any],
        updated_by: str,
    ):
        return cls(
            data={
                "item_id": item_id,
                "product_id": product_id,
                "location_id": location_id,
                "updated_fields": updated_fields,
                "updated_by": updated_by,
            }
        )


class InventoryItemDeletedEvent(BaseModel):
    """Event published when an inventory item is deleted."""

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=InventoryEventType.INVENTORY_ITEM_DELETED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="inventory_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]

    @classmethod
    def create(cls, item_id: int, product_id: int, location_id: int, deleted_by: str):
        return cls(
            data={
                "item_id": item_id,
                "product_id": product_id,
                "location_id": location_id,
                "deleted_by": deleted_by,
            }
        )


class StockLevelChangedEvent(BaseModel):
    """Event published when stock level changes."""

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=InventoryEventType.STOCK_LEVEL_CHANGED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="inventory_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]

    @classmethod
    def create(
        cls,
        item_id: int,
        product_id: int,
        location_id: int,
        old_quantity: int,
        new_quantity: int,
        change_reason: str,
    ):
        return cls(
            data={
                "item_id": item_id,
                "product_id": product_id,
                "location_id": location_id,
                "old_quantity": old_quantity,
                "new_quantity": new_quantity,
                "quantity_change": new_quantity - old_quantity,
                "change_reason": change_reason,
            }
        )


class LowStockAlertEvent(BaseModel):
    """Event published when stock level reaches or falls below reorder point."""

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=InventoryEventType.LOW_STOCK_ALERT)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="inventory_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]

    @classmethod
    def create(
        cls,
        item_id: int,
        product_id: int,
        location_id: int,
        current_quantity: int,
        reorder_point: int,
    ):
        return cls(
            data={
                "item_id": item_id,
                "product_id": product_id,
                "location_id": location_id,
                "current_quantity": current_quantity,
                "reorder_point": reorder_point,
                "shortage": reorder_point - current_quantity,
            }
        )


class StockMovementRecordedEvent(BaseModel):
    """Event published when stock movement is recorded."""

    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = Field(default=InventoryEventType.STOCK_MOVEMENT_RECORDED)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    service: str = Field(default="inventory_service")
    version: str = Field(default="1.0")
    data: Dict[str, Any]

    @classmethod
    def create(
        cls,
        movement_id: int,
        item_id: int,
        product_id: int,
        movement_type: str,
        quantity: int,
        reference_id: Optional[str] = None,
    ):
        return cls(
            data={
                "movement_id": movement_id,
                "item_id": item_id,
                "product_id": product_id,
                "movement_type": movement_type,
                "quantity": quantity,
                "reference_id": reference_id,
            }
        )


# Consumed Events (from other services)


class OrderCreatedEvent(BaseModel):
    """Event consumed when an order is created."""

    event_id: str
    event_type: str = Field(default=OrderEventType.ORDER_CREATED)
    timestamp: str
    service: str
    version: str
    data: Dict[str, Any]


class OrderCancelledEvent(BaseModel):
    """Event consumed when an order is cancelled."""

    event_id: str
    event_type: str = Field(default=OrderEventType.ORDER_CANCELLED)
    timestamp: str
    service: str
    version: str
    data: Dict[str, Any]


class ProductCreatedEvent(BaseModel):
    """Event consumed when a product is created."""

    event_id: str
    event_type: str = Field(default=ProductEventType.PRODUCT_CREATED)
    timestamp: str
    service: str
    version: str
    data: Dict[str, Any]


class ProductUpdatedEvent(BaseModel):
    """Event consumed when a product is updated."""

    event_id: str
    event_type: str = Field(default=ProductEventType.PRODUCT_UPDATED)
    timestamp: str
    service: str
    version: str
    data: Dict[str, Any]


class ProductDeletedEvent(BaseModel):
    """Event consumed when a product is deleted."""

    event_id: str
    event_type: str = Field(default=ProductEventType.PRODUCT_DELETED)
    timestamp: str
    service: str
    version: str
    data: Dict[str, Any]


# Event type mapping for deserialization
INVENTORY_EVENT_MAPPING = {
    InventoryEventType.INVENTORY_ITEM_CREATED: InventoryItemCreatedEvent,
    InventoryEventType.INVENTORY_ITEM_UPDATED: InventoryItemUpdatedEvent,
    InventoryEventType.INVENTORY_ITEM_DELETED: InventoryItemDeletedEvent,
    InventoryEventType.STOCK_LEVEL_CHANGED: StockLevelChangedEvent,
    InventoryEventType.LOW_STOCK_ALERT: LowStockAlertEvent,
    InventoryEventType.STOCK_MOVEMENT_RECORDED: StockMovementRecordedEvent,
}

ORDER_EVENT_MAPPING = {
    OrderEventType.ORDER_CREATED: OrderCreatedEvent,
    OrderEventType.ORDER_CANCELLED: OrderCancelledEvent,
}

PRODUCT_EVENT_MAPPING = {
    ProductEventType.PRODUCT_CREATED: ProductCreatedEvent,
    ProductEventType.PRODUCT_UPDATED: ProductUpdatedEvent,
    ProductEventType.PRODUCT_DELETED: ProductDeletedEvent,
}
