"""
Event publisher for publishing inventory events to Kafka topics.
"""

import json
import logging
from typing import Union, Optional
from confluent_kafka import Producer
from app.events.kafka_config import kafka_config
from app.events.schemas import (
    InventoryItemCreatedEvent,
    InventoryItemUpdatedEvent,
    InventoryItemDeletedEvent,
    StockLevelChangedEvent,
    LowStockAlertEvent,
    StockMovementRecordedEvent,
    BaseEvent,
)

logger = logging.getLogger(__name__)


class EventPublisher:
    """Publishes inventory events to Kafka topics."""

    def __init__(self):
        self.producer = None
        self.inventory_topic = kafka_config.get_topic_name("inventory")

    def _get_producer(self) -> Producer:
        """Get or create Kafka producer instance."""
        if self.producer is None:
            producer_config = kafka_config.get_producer_config()
            self.producer = Producer(producer_config)
            logger.info("Kafka producer initialized")
        return self.producer

    def _delivery_callback(self, err, msg):
        """Callback for message delivery confirmation."""
        if err is not None:
            logger.error(f"Message delivery failed: {err}")
        else:
            logger.debug(
                f"Message delivered to topic {msg.topic()} partition {msg.partition()} offset {msg.offset()}"
            )

    async def publish_event(
        self, event: Union[BaseEvent, dict], topic: str = None, key: str = None
    ):
        """Publish an event to the specified topic or default inventory topic."""
        try:
            topic = topic or self.inventory_topic
            producer = self._get_producer()

            # Convert Pydantic model to dict if needed
            if hasattr(event, "model_dump"):
                event_data = event.model_dump()
            elif isinstance(event, dict):
                event_data = event
            else:
                event_data = event.__dict__

            # Serialize event data to JSON
            message_value = json.dumps(event_data)

            # Use event_id as key if no key provided
            message_key = key or event_data.get("event_id", "default")

            # Produce message to Kafka
            producer.produce(
                topic=topic,
                key=message_key,
                value=message_value,
                callback=self._delivery_callback,
            )

            # Trigger delivery of messages
            producer.poll(0)

            logger.info(
                f"Published event {event_data.get('event_type')} to topic '{topic}' with key '{message_key}'"
            )

        except Exception as e:
            logger.error(f"Failed to publish event: {e}")
            # Don't raise exception to prevent breaking main business logic

    def flush(self):
        """Flush any pending messages."""
        if self.producer:
            self.producer.flush()

    def close(self):
        """Close the producer and clean up resources."""
        if self.producer:
            self.producer.flush()
            # Note: confluent_kafka Producer doesn't have a close method
            self.producer = None
            logger.info("Kafka producer closed")

    async def publish_inventory_item_created(
        self,
        item_id: int,
        product_id: int,
        location_id: int,
        quantity: int,
        created_by: str,
    ):
        """Publish INVENTORY_ITEM_CREATED event."""
        event = InventoryItemCreatedEvent.create(
            item_id=item_id,
            product_id=product_id,
            location_id=location_id,
            quantity=quantity,
            created_by=created_by,
        )
        await self.publish_event(event)

    async def publish_inventory_item_updated(
        self,
        item_id: int,
        product_id: int,
        location_id: int,
        updated_fields: dict,
        updated_by: str,
    ):
        """Publish INVENTORY_ITEM_UPDATED event."""
        event = InventoryItemUpdatedEvent.create(
            item_id=item_id,
            product_id=product_id,
            location_id=location_id,
            updated_fields=updated_fields,
            updated_by=updated_by,
        )
        await self.publish_event(event)

    async def publish_inventory_item_deleted(
        self, item_id: int, product_id: int, location_id: int, deleted_by: str
    ):
        """Publish INVENTORY_ITEM_DELETED event."""
        event = InventoryItemDeletedEvent.create(
            item_id=item_id,
            product_id=product_id,
            location_id=location_id,
            deleted_by=deleted_by,
        )
        await self.publish_event(event)

    async def publish_stock_level_changed(
        self,
        item_id: int,
        product_id: int,
        location_id: int,
        old_quantity: int,
        new_quantity: int,
        change_reason: str,
    ):
        """Publish STOCK_LEVEL_CHANGED event."""
        event = StockLevelChangedEvent.create(
            item_id=item_id,
            product_id=product_id,
            location_id=location_id,
            old_quantity=old_quantity,
            new_quantity=new_quantity,
            change_reason=change_reason,
        )
        await self.publish_event(event)

    async def publish_low_stock_alert(
        self,
        item_id: int,
        product_id: int,
        location_id: int,
        current_quantity: int,
        reorder_point: int,
    ):
        """Publish LOW_STOCK_ALERT event."""
        event = LowStockAlertEvent.create(
            item_id=item_id,
            product_id=product_id,
            location_id=location_id,
            current_quantity=current_quantity,
            reorder_point=reorder_point,
        )
        await self.publish_event(event)

    async def publish_stock_movement_recorded(
        self,
        movement_id: int,
        item_id: int,
        product_id: int,
        movement_type: str,
        quantity: int,
        reference_id: Optional[str] = None,
    ):
        """Publish STOCK_MOVEMENT_RECORDED event."""
        event = StockMovementRecordedEvent.create(
            movement_id=movement_id,
            item_id=item_id,
            product_id=product_id,
            movement_type=movement_type,
            quantity=quantity,
            reference_id=reference_id,
        )
        await self.publish_event(event)


# Global event publisher instance
event_publisher = EventPublisher()
